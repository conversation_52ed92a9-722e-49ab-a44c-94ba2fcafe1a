import React from "react";
import { useMatrixChat } from "../../hooks/useMatrixChat";
import MessengerChatBubble from "./MessengerChatBubble";
import LoadingSpinner from "../ui/LoadingSpinner";

interface EmbeddableChatWindowProps {
  primaryColor?: string;
  companyName?: string;
  welcomeMessage?: string;
}

const EmbeddableChatWindow: React.FC<EmbeddableChatWindowProps> = ({
  primaryColor = "#3B82F6",
  companyName = "Hỗ trợ khách hàng",
  welcomeMessage = "Chúng tôi sẵn sàng hỗ trợ bạn. Vui lòng cho biết tên để bắt đầu cuộc trò chuyện.",
}) => {
  const {
    chatState,
    userInfo,
    messages,
    newMessage,
    setNewMessage,
    containerRef,
    toggleChat,
    updateUserInfo,
    startChat,
    sendMessage,
  } = useMatrixChat();

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (chatState.isStarted) {
        sendMessage();
      } else if (userInfo.name.trim()) {
        startChat();
      }
    }
  };

  const isFormValid = userInfo.name.trim().length > 0;

  return (
    <>
      {/* Chat toggle button */}
      <button
        onClick={toggleChat}
        className="fixed bottom-4 right-4 z-50 w-16 h-16 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
        style={{
          background: `linear-gradient(to right, ${primaryColor}, ${primaryColor}dd)`,
        }}
        aria-label="Toggle chat support"
      >
        {chatState.isOpen ? (
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        ) : (
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
              clipRule="evenodd"
            />
          </svg>
        )}
      </button>

      {/* Chat window */}
      {chatState.isOpen && (
        <div className="fixed bottom-20 right-4 z-50 w-96 h-[500px] bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden">
          {!chatState.isStarted ? (
            /* Welcome form */
            <div className="h-full flex flex-col">
              {/* Header */}
              <div
                className="text-white p-4"
                style={{
                  background: `linear-gradient(to right, ${primaryColor}, ${primaryColor}dd)`,
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <svg
                        className="w-6 h-6"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold">{companyName}</h3>
                      <p className="text-sm text-blue-100">
                        Luôn sẵn sàng hỗ trợ bạn
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={toggleChat}
                    className="text-white hover:bg-white hover:bg-opacity-20 p-2 rounded-full transition-colors"
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Form content */}
              <div className="flex-1 p-6 space-y-6">
                <div className="text-center">
                  <h4 className="text-xl font-semibold text-gray-800 mb-2">
                    Xin chào! 👋
                  </h4>
                  <p className="text-gray-600">{welcomeMessage}</p>
                </div>

                <div className="space-y-4">
                  <input
                    type="text"
                    placeholder="Tên của bạn *"
                    value={userInfo.name}
                    onChange={(e) => updateUserInfo("name", e.target.value)}
                    onKeyDown={handleKeyPress}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-100 outline-none transition-all"
                  />

                  <input
                    type="text"
                    placeholder="Số điện thoại (tùy chọn)"
                    value={userInfo.phone}
                    onChange={(e) => updateUserInfo("phone", e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-100 outline-none transition-all"
                  />

                  <input
                    type="text"
                    placeholder="Chủ đề cần hỗ trợ (tùy chọn)"
                    value={userInfo.topicName}
                    onChange={(e) =>
                      updateUserInfo("topicName", e.target.value)
                    }
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-100 outline-none transition-all"
                  />
                </div>

                <button
                  onClick={startChat}
                  disabled={!isFormValid || chatState.isLoading}
                  className={`w-full py-3 rounded-xl font-medium transition-all duration-200 ${
                    isFormValid && !chatState.isLoading
                      ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 transform hover:scale-[1.02]"
                      : "bg-gray-300 text-gray-500 cursor-not-allowed"
                  }`}
                >
                  {chatState.isLoading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <LoadingSpinner size="sm" color="white" />
                      <span>Đang kết nối...</span>
                    </div>
                  ) : (
                    "Bắt đầu trò chuyện"
                  )}
                </button>
              </div>
            </div>
          ) : (
            /* Chat interface */
            <div className="h-full flex flex-col">
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <svg
                        className="w-6 h-6"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold">{companyName}</h3>
                      <div className="flex items-center space-x-2">
                        <div
                          className={`w-2 h-2 rounded-full ${
                            chatState.isConnected
                              ? "bg-green-400"
                              : "bg-yellow-400"
                          }`}
                        ></div>
                        <p className="text-sm text-blue-100">
                          {chatState.isConnected
                            ? "Đang hoạt động"
                            : "Đang kết nối..."}
                        </p>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={toggleChat}
                    className="text-white hover:bg-white hover:bg-opacity-20 p-2 rounded-full transition-colors"
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Messages */}
              <div
                ref={containerRef}
                className="flex-1 overflow-y-auto p-4 space-y-3 bg-white messenger-scrollbar"
              >
                {messages.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg
                        className="w-8 h-8 text-blue-500"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <p className="text-gray-500 mb-2">
                      Chào mừng bạn đến với hỗ trợ!
                    </p>
                    <p className="text-sm text-gray-400">
                      Hãy gửi tin nhắn để bắt đầu cuộc trò chuyện
                    </p>
                  </div>
                ) : (
                  messages.map((msg) => (
                    <MessengerChatBubble key={msg.eventId} message={msg} />
                  ))
                )}
              </div>

              {/* Input */}
              <div className="p-4 bg-white border-t border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="flex-1">
                    <input
                      type="text"
                      placeholder="Nhập tin nhắn..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={handleKeyPress}
                      disabled={!chatState.isConnected || chatState.isLoading}
                      className="w-full px-4 py-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:bg-blue-50 outline-none transition-all"
                    />
                  </div>
                  <button
                    onClick={sendMessage}
                    disabled={
                      !newMessage.trim() ||
                      !chatState.isConnected ||
                      chatState.isLoading
                    }
                    className={`w-12 h-12 rounded-full transition-all duration-200 flex items-center justify-center ${
                      newMessage.trim() &&
                      chatState.isConnected &&
                      !chatState.isLoading
                        ? "bg-blue-500 text-white hover:bg-blue-600 transform hover:scale-105 shadow-lg"
                        : "bg-gray-200 text-gray-400 cursor-not-allowed"
                    }`}
                  >
                    {chatState.isLoading ? (
                      <LoadingSpinner size="sm" color="white" />
                    ) : (
                      <svg
                        className="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default EmbeddableChatWindow;

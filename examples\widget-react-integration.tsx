import React, { useState, useCallback } from 'react';
import { SimpleChatWidget, ChatWidgetConfig } from '../src/widget/react-component';

// Example React component showing how to integrate the chat widget
const ChatWidgetDemo: React.FC = () => {
  const [config, setConfig] = useState<Partial<ChatWidgetConfig>>({
    primaryColor: '#3B82F6',
    companyName: 'Demo Company',
    welcomeMessage: 'Hello! How can we help you today?',
    position: 'bottom-right',
    showBranding: true,
  });

  const [events, setEvents] = useState<string[]>([]);

  const logEvent = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setEvents(prev => [...prev, `[${timestamp}] ${message}`]);
  }, []);

  const handleToggle = useCallback((isOpen: boolean) => {
    logEvent(`Chat ${isOpen ? 'opened' : 'closed'}`);
  }, [logEvent]);

  const handleMessage = useCallback((message: any) => {
    logEvent(`New message: ${message.content || 'Unknown message'}`);
  }, [logEvent]);

  const handleChatStarted = useCallback((userInfo: any) => {
    logEvent(`Chat started by: ${userInfo.name || 'Unknown user'}`);
  }, [logEvent]);

  const updateConfig = (updates: Partial<ChatWidgetConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
    logEvent(`Config updated: ${JSON.stringify(updates)}`);
  };

  const presetConfigs = {
    default: {
      primaryColor: '#3B82F6',
      companyName: 'Demo Company',
      welcomeMessage: 'Hello! How can we help you today?',
    },
    red: {
      primaryColor: '#EF4444',
      companyName: 'Red Support Team',
      welcomeMessage: 'Welcome to our red-themed support!',
    },
    green: {
      primaryColor: '#10B981',
      companyName: 'Green Support Team',
      welcomeMessage: 'Eco-friendly support team here!',
    },
    purple: {
      primaryColor: '#8B5CF6',
      companyName: 'Purple Premium Support',
      welcomeMessage: 'Premium support experience awaits!',
    },
  };

  return (
    <div style={{ 
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      padding: '40px',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      color: 'white'
    }}>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <div style={{ textAlign: 'center', marginBottom: '60px' }}>
          <h1 style={{ fontSize: '3rem', marginBottom: '20px', fontWeight: 700 }}>
            🚀 React Chat Widget Demo
          </h1>
          <p style={{ fontSize: '1.2rem', opacity: 0.9, maxWidth: '600px', margin: '0 auto' }}>
            Experience our chat widget integrated as a React component.
            Perfect for React applications with full TypeScript support.
          </p>
        </div>

        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          padding: '40px',
          borderRadius: '20px',
          backdropFilter: 'blur(10px)',
          marginBottom: '40px'
        }}>
          <h2>Current Configuration</h2>
          <div style={{
            background: 'rgba(0, 0, 0, 0.3)',
            padding: '20px',
            borderRadius: '10px',
            fontFamily: 'monospace',
            fontSize: '14px',
            marginBottom: '20px'
          }}>
            <pre>{JSON.stringify(config, null, 2)}</pre>
          </div>

          <h3>🎨 Try Different Themes</h3>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '20px',
            margin: '20px 0'
          }}>
            {Object.entries(presetConfigs).map(([name, preset]) => (
              <button
                key={name}
                onClick={() => updateConfig(preset)}
                style={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  border: '2px solid rgba(255, 255, 255, 0.3)',
                  color: 'white',
                  padding: '15px 20px',
                  borderRadius: '10px',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  textAlign: 'center',
                  fontSize: '16px',
                  textTransform: 'capitalize'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.3)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                {name} Theme
              </button>
            ))}
          </div>

          <h3>⚙️ Custom Configuration</h3>
          <div style={{ display: 'grid', gap: '15px', marginBottom: '20px' }}>
            <div>
              <label style={{ display: 'block', marginBottom: '5px' }}>Primary Color:</label>
              <input
                type="color"
                value={config.primaryColor}
                onChange={(e) => updateConfig({ primaryColor: e.target.value })}
                style={{ width: '100px', height: '40px', border: 'none', borderRadius: '5px' }}
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '5px' }}>Company Name:</label>
              <input
                type="text"
                value={config.companyName}
                onChange={(e) => updateConfig({ companyName: e.target.value })}
                style={{
                  padding: '10px',
                  borderRadius: '5px',
                  border: 'none',
                  width: '100%',
                  maxWidth: '300px'
                }}
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '5px' }}>Welcome Message:</label>
              <textarea
                value={config.welcomeMessage}
                onChange={(e) => updateConfig({ welcomeMessage: e.target.value })}
                style={{
                  padding: '10px',
                  borderRadius: '5px',
                  border: 'none',
                  width: '100%',
                  maxWidth: '400px',
                  height: '80px',
                  resize: 'vertical'
                }}
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '5px' }}>Position:</label>
              <select
                value={config.position}
                onChange={(e) => updateConfig({ position: e.target.value as any })}
                style={{
                  padding: '10px',
                  borderRadius: '5px',
                  border: 'none',
                  width: '200px'
                }}
              >
                <option value="bottom-right">Bottom Right</option>
                <option value="bottom-left">Bottom Left</option>
                <option value="top-right">Top Right</option>
                <option value="top-left">Top Left</option>
              </select>
            </div>
          </div>
        </div>

        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          padding: '40px',
          borderRadius: '20px',
          backdropFilter: 'blur(10px)',
          marginBottom: '40px'
        }}>
          <h2>📋 Integration Code</h2>
          <p>Here's how this React component is implemented:</p>
          
          <div style={{
            background: 'rgba(0, 0, 0, 0.3)',
            padding: '20px',
            borderRadius: '10px',
            fontFamily: 'monospace',
            fontSize: '14px',
            overflow: 'auto'
          }}>
            <pre>{`import { SimpleChatWidget } from '@yourcompany/simple-chat-widget';
import '@yourcompany/simple-chat-widget/dist/style.css';

function App() {
  return (
    <SimpleChatWidget
      primaryColor="${config.primaryColor}"
      companyName="${config.companyName}"
      welcomeMessage="${config.welcomeMessage}"
      position="${config.position}"
      onToggle={(isOpen) => console.log('Chat toggled:', isOpen)}
      onMessage={(message) => console.log('New message:', message)}
      onChatStarted={(userInfo) => console.log('Chat started:', userInfo)}
    />
  );
}`}</pre>
          </div>
        </div>

        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          padding: '40px',
          borderRadius: '20px',
          backdropFilter: 'blur(10px)'
        }}>
          <h2>🔧 Event Log</h2>
          <div style={{
            background: 'rgba(0, 0, 0, 0.3)',
            padding: '15px',
            borderRadius: '8px',
            fontFamily: 'monospace',
            fontSize: '12px',
            maxHeight: '200px',
            overflowY: 'auto'
          }}>
            {events.length === 0 ? (
              <div>Event log will appear here...</div>
            ) : (
              events.map((event, index) => (
                <div key={index}>{event}</div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* The actual chat widget */}
      <SimpleChatWidget
        {...config}
        onToggle={handleToggle}
        onMessage={handleMessage}
        onChatStarted={handleChatStarted}
      />
    </div>
  );
};

export default ChatWidgetDemo;

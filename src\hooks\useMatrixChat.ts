import { useState, useRef, useCallback, useEffect } from "react";
import * as sdk from "matrix-js-sdk";

// Types
export interface Message {
  sender: string;
  senderName: string;
  content: string;
  timestamp: number;
  eventId: string;
  type: string;
  contentUrl: string | null;
  isCurrentUser: boolean;
}

export interface ChatState {
  isOpen: boolean;
  isStarted: boolean;
  isLoading: boolean;
  error: string | null;
  isConnected: boolean;
}

export interface UserInfo {
  name: string;
  phone: string;
  topicName: string;
}

// Custom hook for Matrix chat functionality
export const useMatrixChat = () => {
  const [chatState, setChatState] = useState<ChatState>({
    isOpen: false,
    isStarted: false,
    isLoading: false,
    error: null,
    isConnected: false,
  });

  const [userInfo, setUserInfo] = useState<UserInfo>({
    name: "",
    phone: "",
    topicName: "",
  });

  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");

  const clientRef = useRef<sdk.MatrixClient | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Utility function for MXC URL conversion
  const mxcUrlToHttp = useCallback(
    (mxcUrl: string, baseUrl = "https://matrix-synapse.longvan.vn"): string => {
      if (!mxcUrl.startsWith("mxc://")) return mxcUrl;
      const mediaId = mxcUrl.replace("mxc://", "");
      return `${baseUrl}/_matrix/media/v3/download/${mediaId}`;
    },
    []
  );

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (containerRef.current && messages.length > 0) {
      const container = containerRef.current;
      // Smooth scroll to bottom
      setTimeout(() => {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: "smooth",
        });
      }, 100);
    }
  }, [messages]);

  // Load thread messages
  const loadThreadMessages = useCallback(async () => {
    const client = clientRef.current;
    const roomId = "!MnJuNdvpjxlMZkfjcn:matrix-synapse.longvan.vn";
    const threadId = "$BJHuwFmtvl2LLO1Fs_JfBGyEzhU6eDgwFWiAmIeTJrk";

    if (!client) return;

    try {
      const room = client.getRoom(roomId);
      if (!room) return;

      const response = await client.relations(
        roomId,
        threadId,
        "m.thread",
        "m.room.message",
        { limit: 50 }
      );

      const msgs = response.events
        .map((event: any) => {
          const sender = event.getSender();
          const member = room.getMember(sender);
          const senderName = member ? member.name : sender;

          return {
            sender,
            senderName,
            content: event.getContent().body || "",
            timestamp: event.getTs(),
            eventId: event.getId(),
            type: event.getContent().msgtype,
            contentUrl: event.getContent().url
              ? mxcUrlToHttp(event.getContent().url)
              : null,
            isCurrentUser: sender === client.getUserId(),
          };
        })
        .reverse();

      setMessages(msgs);
    } catch (error) {
      console.error("Error loading messages:", error);
      setChatState((prev) => ({
        ...prev,
        error:
          error instanceof Error ? error.message : "Failed to load messages",
      }));
    }
  }, [mxcUrlToHttp]);

  // Matrix client login and setup with enhanced loading states
  const handleLogin = useCallback(async () => {
    try {
      setChatState((prev) => ({ ...prev, isLoading: true, error: null }));

      // Simulate realistic loading steps
      await new Promise((resolve) => setTimeout(resolve, 500)); // Initial delay

      const tempClient = sdk.createClient({
        baseUrl: "https://matrix-synapse.longvan.vn",
      });

      // Login step
      const loginResponse = await tempClient.loginWithPassword("demo", "demo");
      await new Promise((resolve) => setTimeout(resolve, 300)); // Login processing

      const matrixClient = sdk.createClient({
        baseUrl: "https://matrix-synapse.longvan.vn",
        accessToken: loginResponse.access_token,
        userId: loginResponse.user_id,
      });

      clientRef.current = matrixClient;

      // Start client with loading feedback
      matrixClient.startClient({
        initialSyncLimit: 8,
        lazyLoadMembers: true,
        pollTimeout: 30000,
      });

      matrixClient.once(sdk.ClientEvent.Sync, async (state: string) => {
        if (state === "PREPARED") {
          console.log("✅ Matrix client ready");

          // Final loading step
          await new Promise((resolve) => setTimeout(resolve, 200));

          setChatState((prev) => ({
            ...prev,
            isLoading: false,
            isConnected: true,
          }));
          await loadThreadMessages();
        }
      });

      matrixClient.on(sdk.RoomEvent.Timeline, async (event: any) => {
        if (event.getType() === "m.room.message") {
          await loadThreadMessages();
        }
      });
    } catch (error) {
      console.error("❌ Login error:", error);
      setChatState((prev) => ({
        ...prev,
        isLoading: false,
        error: null, // Bỏ error message để không hiển thị UI lỗi
      }));
    }
  }, [loadThreadMessages]);

  // Send message with enhanced loading states
  const sendMessage = useCallback(async () => {
    const client = clientRef.current;
    const roomId = "!MnJuNdvpjxlMZkfjcn:matrix-synapse.longvan.vn";
    const threadId = "$BJHuwFmtvl2LLO1Fs_JfBGyEzhU6eDgwFWiAmIeTJrk";

    if (!client || !roomId || !threadId || !newMessage.trim()) return;

    const messageToSend = newMessage.trim();

    try {
      // Clear input immediately for better UX
      setNewMessage("");
      setChatState((prev) => ({ ...prev, isLoading: true }));

      // Add slight delay for visual feedback
      await new Promise((resolve) => setTimeout(resolve, 100));

      await client.sendEvent(roomId, sdk.EventType.RoomMessage, {
        body: messageToSend,
        msgtype: sdk.MsgType.Text,
        "m.relates_to": {
          rel_type: sdk.RelationType.Thread,
          event_id: threadId,
        },
      });

      await loadThreadMessages();
      setChatState((prev) => ({ ...prev, isLoading: false }));

      // Auto-scroll to bottom after sending message
      setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.scrollTo({
            top: containerRef.current.scrollHeight,
            behavior: "smooth",
          });
        }
      }, 200);
    } catch (error) {
      console.error("❌ Send message error:", error);
      // Restore message on error
      setNewMessage(messageToSend);
      setChatState((prev) => ({
        ...prev,
        isLoading: false,
        error: null, // Bỏ error message để không hiển thị UI lỗi
      }));
    }
  }, [newMessage, loadThreadMessages]);

  // Toggle chat visibility
  const toggleChat = useCallback(() => {
    setChatState((prev) => ({ ...prev, isOpen: !prev.isOpen }));
  }, []);

  // Update user info
  const updateUserInfo = useCallback((field: keyof UserInfo, value: string) => {
    setUserInfo((prev) => ({ ...prev, [field]: value }));
  }, []);

  // Start chat
  const startChat = useCallback(async () => {
    setChatState((prev) => ({
      ...prev,
      isStarted: true,
      isLoading: true,
      error: null,
    }));

    try {
      await handleLogin();
    } catch (error) {
      setChatState((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to start chat",
      }));
    }
  }, [handleLogin]);

  // Check localStorage on mount
  useEffect(() => {
    const savedRoomId = localStorage.getItem("roomId");
    const savedThreadId = localStorage.getItem("threadId");

    if (savedRoomId && savedThreadId) {
      setChatState((prev) => ({ ...prev, isStarted: true }));

      // Initialize connection asynchronously
      (async () => {
        try {
          await handleLogin();
        } catch (error) {
          setChatState((prev) => ({
            ...prev,
            error:
              error instanceof Error ? error.message : "Failed to reconnect",
          }));
        }
      })();
    }
  }, [handleLogin]);

  return {
    chatState,
    setChatState,
    userInfo,
    messages,
    newMessage,
    setNewMessage,
    containerRef,
    toggleChat,
    updateUserInfo,
    startChat,
    sendMessage,
  };
};

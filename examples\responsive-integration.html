<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Responsive Chat Widget Integration</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: white;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
      }

      .header {
        text-align: center;
        margin-bottom: 3rem;
      }

      .header h1 {
        font-size: clamp(2rem, 5vw, 4rem);
        margin-bottom: 1rem;
        font-weight: 800;
      }

      .header p {
        font-size: clamp(1rem, 2.5vw, 1.25rem);
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
      }

      .demo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
      }

      .demo-card {
        background: rgba(255, 255, 255, 0.1);
        padding: 2rem;
        border-radius: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: transform 0.3s ease;
      }

      .demo-card:hover {
        transform: translateY(-5px);
      }

      .demo-card h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .demo-card p {
        opacity: 0.9;
        margin-bottom: 1.5rem;
        line-height: 1.6;
      }

      .demo-button {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
        width: 100%;
        font-size: 1rem;
      }

      .demo-button:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      .device-preview {
        background: rgba(255, 255, 255, 0.1);
        padding: 2rem;
        border-radius: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
      }

      .device-preview h2 {
        margin-bottom: 2rem;
        font-size: 2rem;
      }

      .device-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
      }

      .info-item {
        background: rgba(255, 255, 255, 0.1);
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
      }

      .info-item .label {
        font-size: 0.875rem;
        opacity: 0.8;
        margin-bottom: 0.5rem;
      }

      .info-item .value {
        font-size: 1.25rem;
        font-weight: 600;
      }

      .responsive-note {
        background: rgba(255, 255, 255, 0.1);
        padding: 1.5rem;
        border-radius: 15px;
        margin-top: 2rem;
        border-left: 4px solid rgba(255, 255, 255, 0.5);
      }

      .responsive-note h4 {
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
      }

      .responsive-note p {
        opacity: 0.9;
        font-size: 0.95rem;
      }

      /* Responsive breakpoints demonstration */
      @media (max-width: 1200px) {
        .container {
          padding: 1.5rem;
        }
      }

      @media (max-width: 768px) {
        .container {
          padding: 1rem;
        }

        .demo-grid {
          grid-template-columns: 1fr;
          gap: 1.5rem;
        }

        .demo-card {
          padding: 1.5rem;
        }

        .device-info {
          grid-template-columns: 1fr;
        }
      }

      @media (max-width: 480px) {
        .demo-card {
          padding: 1rem;
        }

        .demo-card h3 {
          font-size: 1.25rem;
        }

        .device-preview {
          padding: 1rem;
        }
      }

      /* Chat widget responsive overrides */
      @media (max-width: 768px) {
        #longvan-chat-container {
          width: 100% !important;
          height: 100% !important;
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          right: 0 !important;
          bottom: 0 !important;
          z-index: 10000 !important;
        }

        #longvan-chat-iframe {
          width: 100% !important;
          height: 100% !important;
          border-radius: 0 !important;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>📱 Responsive Chat Widget</h1>
        <p>
          Experience how our chat widget adapts perfectly to different screen
          sizes and devices. Try resizing your browser or viewing on different
          devices.
        </p>
      </div>

      <div class="demo-grid">
        <div class="demo-card">
          <h3>🖥️ Desktop Experience</h3>
          <p>
            On desktop, the chat widget appears as a floating window in the
            corner with full features and hover effects.
          </p>
          <button class="demo-button" onclick="setDesktopMode()">
            Try Desktop Mode
          </button>
        </div>

        <div class="demo-card">
          <h3>📱 Mobile Experience</h3>
          <p>
            On mobile devices, the widget can expand to full screen for better
            usability and touch interaction.
          </p>
          <button class="demo-button" onclick="setMobileMode()">
            Try Mobile Mode
          </button>
        </div>

        <div class="demo-card">
          <h3>🎨 Adaptive Styling</h3>
          <p>
            Colors, fonts, and spacing automatically adjust based on screen size
            and device capabilities.
          </p>
          <button class="demo-button" onclick="toggleTheme()">
            Toggle Theme
          </button>
        </div>
      </div>

      <div class="device-preview">
        <h2>📊 Current Device Info</h2>
        <div class="device-info">
          <div class="info-item">
            <div class="label">Screen Width</div>
            <div class="value" id="screenWidth">-</div>
          </div>
          <div class="info-item">
            <div class="label">Screen Height</div>
            <div class="value" id="screenHeight">-</div>
          </div>
          <div class="info-item">
            <div class="label">Device Type</div>
            <div class="value" id="deviceType">-</div>
          </div>
          <div class="info-item">
            <div class="label">Orientation</div>
            <div class="value" id="orientation">-</div>
          </div>
        </div>

        <div class="responsive-note">
          <h4>💡 Responsive Features</h4>
          <p>
            The chat widget automatically adjusts its size, position, and
            behavior based on your device. On mobile, it can go full-screen for
            better usability. On desktop, it maintains a compact floating
            design.
          </p>
        </div>
      </div>
    </div>

    <!-- Load the embed script -->
    <script src="http://localhost:3000/embed.js"></script>

    <script>
      let currentTheme = "blue";
      let currentMode = "auto";

      // Theme configurations
      const themes = {
        blue: {
          primaryColor: "#3b82f6",
          companyName: "Blue Theme Support",
          welcomeMessage: "Hello! Blue theme is active.",
        },
        purple: {
          primaryColor: "#8b5cf6",
          companyName: "Purple Theme Support",
          welcomeMessage: "Welcome! Purple theme is now active.",
        },
        green: {
          primaryColor: "#10b981",
          companyName: "Green Theme Support",
          welcomeMessage: "Hi there! Green theme is ready.",
        },
        red: {
          primaryColor: "#ef4444",
          companyName: "Red Theme Support",
          welcomeMessage: "Greetings! Red theme is live.",
        },
      };

      // Initialize widget
      function initializeWidget(config = {}) {
        const defaultConfig = {
          primaryColor: "#3b82f6",
          companyName: "Responsive Demo",
          welcomeMessage:
            "Hello! Try resizing your browser to see responsive behavior.",
          position: "bottom-right",
          responsive: true,
          width: getResponsiveWidth(),
          height: getResponsiveHeight(),
        };

        LongvanChat.destroy();
        LongvanChat.init({ ...defaultConfig, ...config });
      }

      // Get responsive width based on screen size
      function getResponsiveWidth() {
        const width = window.innerWidth;
        if (width < 480) return "100%";
        if (width < 768) return "90%";
        if (width < 1024) return "380px";
        return "400px";
      }

      // Get responsive height based on screen size
      function getResponsiveHeight() {
        const height = window.innerHeight;
        if (window.innerWidth < 768) return "100vh";
        if (height < 600) return "80vh";
        return "600px";
      }

      // Set desktop mode
      function setDesktopMode() {
        currentMode = "desktop";
        initializeWidget({
          width: "400px",
          height: "600px",
          position: "bottom-right",
        });
      }

      // Set mobile mode
      function setMobileMode() {
        currentMode = "mobile";
        initializeWidget({
          width: "100%",
          height: "100vh",
          position: "bottom-right",
        });
      }

      // Toggle theme
      function toggleTheme() {
        const themeKeys = Object.keys(themes);
        const currentIndex = themeKeys.indexOf(currentTheme);
        const nextIndex = (currentIndex + 1) % themeKeys.length;
        currentTheme = themeKeys[nextIndex];

        initializeWidget(themes[currentTheme]);
      }

      // Update device info
      function updateDeviceInfo() {
        const width = window.innerWidth;
        const height = window.innerHeight;

        document.getElementById("screenWidth").textContent = width + "px";
        document.getElementById("screenHeight").textContent = height + "px";

        // Determine device type
        let deviceType = "Desktop";
        if (width < 480) deviceType = "Mobile";
        else if (width < 768) deviceType = "Mobile Large";
        else if (width < 1024) deviceType = "Tablet";

        document.getElementById("deviceType").textContent = deviceType;

        // Determine orientation
        const orientation = width > height ? "Landscape" : "Portrait";
        document.getElementById("orientation").textContent = orientation;
      }

      // Handle window resize
      function handleResize() {
        updateDeviceInfo();

        // Auto-adjust widget if in auto mode
        if (currentMode === "auto") {
          initializeWidget({
            width: getResponsiveWidth(),
            height: getResponsiveHeight(),
          });
        }
      }

      // Initialize on page load
      document.addEventListener("DOMContentLoaded", function () {
        updateDeviceInfo();
        setTimeout(() => initializeWidget(), 1000);
      });

      // Listen for resize events
      window.addEventListener("resize", debounce(handleResize, 300));

      // Listen for orientation change
      window.addEventListener("orientationchange", function () {
        setTimeout(handleResize, 100);
      });

      // Debounce function
      function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
          const later = () => {
            clearTimeout(timeout);
            func(...args);
          };
          clearTimeout(timeout);
          timeout = setTimeout(later, wait);
        };
      }

      // Listen for chat widget events
      window.addEventListener("message", function (event) {
        if (event.data.source === "longvan-chat") {
          console.log("Chat Event:", event.data.type, event.data.data);
        }
      });
    </script>
  </body>
</html>

#!/bin/bash

# Publishing script for messenger chat widget
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Publishing Messenger Chat Widget...${NC}"

# Check if we're on main branch
BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [ "$BRANCH" != "main" ] && [ "$BRANCH" != "master" ]; then
    echo -e "${RED}❌ You must be on main/master branch to publish${NC}"
    exit 1
fi

# Check if working directory is clean
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${RED}❌ Working directory is not clean. Please commit your changes.${NC}"
    exit 1
fi

# Get current version
CURRENT_VERSION=$(node -p "require('./package.json').version")
echo -e "${BLUE}📋 Current version: ${CURRENT_VERSION}${NC}"

# Ask for version bump type
echo -e "${YELLOW}🔢 Select version bump type:${NC}"
echo "1) patch (bug fixes)"
echo "2) minor (new features)"
echo "3) major (breaking changes)"
echo "4) custom version"
read -p "Enter choice (1-4): " choice

case $choice in
    1)
        VERSION_TYPE="patch"
        ;;
    2)
        VERSION_TYPE="minor"
        ;;
    3)
        VERSION_TYPE="major"
        ;;
    4)
        read -p "Enter custom version: " CUSTOM_VERSION
        VERSION_TYPE="--new-version $CUSTOM_VERSION"
        ;;
    *)
        echo -e "${RED}❌ Invalid choice${NC}"
        exit 1
        ;;
esac

# Bump version
echo -e "${BLUE}📈 Bumping version...${NC}"
if [ "$choice" = "4" ]; then
    npm version $CUSTOM_VERSION --no-git-tag-version
else
    npm version $VERSION_TYPE --no-git-tag-version
fi

NEW_VERSION=$(node -p "require('./package.json').version")
echo -e "${GREEN}✅ New version: ${NEW_VERSION}${NC}"

# Build the package
echo -e "${BLUE}🏗️  Building package...${NC}"
npm run build

# Run tests one more time
echo -e "${BLUE}🧪 Running final tests...${NC}"
npm run test

# Create git tag
echo -e "${BLUE}🏷️  Creating git tag...${NC}"
git add package.json
git commit -m "chore: bump version to ${NEW_VERSION}"
git tag "v${NEW_VERSION}"

# Ask for confirmation
echo -e "${YELLOW}⚠️  Ready to publish version ${NEW_VERSION}${NC}"
echo "This will:"
echo "  - Push commits and tags to git"
echo "  - Publish to npm registry"
read -p "Continue? (y/N): " confirm

if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo -e "${YELLOW}❌ Publishing cancelled${NC}"
    exit 1
fi

# Push to git
echo -e "${BLUE}📤 Pushing to git...${NC}"
git push origin $BRANCH
git push origin "v${NEW_VERSION}"

# Publish to npm
echo -e "${BLUE}📦 Publishing to npm...${NC}"
npm publish --access public

echo -e "${GREEN}🎉 Successfully published version ${NEW_VERSION}!${NC}"
echo ""
echo -e "${BLUE}📋 Next steps:${NC}"
echo "  - Update documentation if needed"
echo "  - Create GitHub release"
echo "  - Notify users about the new version"

import { useState, useEffect, useRef, useCallback } from "react";
import * as sdk from "matrix-js-sdk";

// Widget configuration interface
export interface ChatWidgetConfig {
  primaryColor?: string;
  secondaryColor?: string;
  companyName?: string;
  welcomeMessage?: string;
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
  matrixServer?: string;
  username?: string;
  password?: string;
  roomId?: string;
  threadId?: string;
  zIndex?: number;
  width?: string;
  height?: string;
  autoOpen?: boolean;
  showBranding?: boolean;
}

export interface Message {
  eventId: string;
  content: string;
  senderName: string;
  timestamp: number;
  isCurrentUser: boolean;
  contentUrl?: string;
}

interface ChatState {
  isOpen: boolean;
  isStarted: boolean;
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
}

interface UserInfo {
  name: string;
  email: string;
}

export const useStandaloneMatrixChat = (config: ChatWidgetConfig) => {
  const [chatState, setChatState] = useState<ChatState>({
    isOpen: false,
    isStarted: false,
    isConnected: false,
    isLoading: false,
    error: null,
  });

  const [userInfo, setUserInfo] = useState<UserInfo>({
    name: "",
    email: "",
  });

  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const containerRef = useRef<HTMLDivElement>(null);
  const clientRef = useRef<any>(null);

  // Load thread messages
  const loadThreadMessages = useCallback(async () => {
    const client = clientRef.current;
    if (!client || !config.roomId || !config.threadId) return;

    try {
      const room = client.getRoom(config.roomId);
      if (!room) return;

      const events = room.getLiveTimeline().getEvents();
      const threadEvents = events.filter((event: any) => {
        const relation = event.getRelation();
        return (
          event.getType() === "m.room.message" &&
          relation?.rel_type === "m.thread" &&
          relation?.event_id === config.threadId
        );
      });

      const formattedMessages: Message[] = threadEvents.map((event: any) => ({
        eventId: event.getId(),
        content: event.getContent().body || "",
        senderName: event.getSender() || "Unknown",
        timestamp: event.getTs(),
        isCurrentUser: event.getSender() === client.getUserId(),
        contentUrl: event.getContent().url,
      }));

      setMessages(formattedMessages);

      // Auto-scroll to bottom
      setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = containerRef.current.scrollHeight;
        }
      }, 100);
    } catch (error) {
      console.error("Error loading messages:", error);
    }
  }, [config.roomId, config.threadId]);

  // Matrix client login and setup
  const handleLogin = useCallback(async () => {
    try {
      setChatState((prev) => ({ ...prev, isLoading: true, error: null }));

      await new Promise((resolve) => setTimeout(resolve, 500));

      const tempClient = sdk.createClient({
        baseUrl: config.matrixServer!,
      });

      const loginResponse = await tempClient.loginWithPassword(
        config.username!,
        config.password!
      );
      await new Promise((resolve) => setTimeout(resolve, 300));

      const matrixClient = sdk.createClient({
        baseUrl: config.matrixServer!,
        accessToken: loginResponse.access_token,
        userId: loginResponse.user_id,
      });

      clientRef.current = matrixClient;

      matrixClient.startClient({
        initialSyncLimit: 8,
        lazyLoadMembers: true,
        pollTimeout: 30000,
      });

      matrixClient.once(sdk.ClientEvent.Sync, async (state: string) => {
        if (state === "PREPARED") {
          console.log("✅ Matrix client ready");

          await new Promise((resolve) => setTimeout(resolve, 200));

          setChatState((prev) => ({
            ...prev,
            isLoading: false,
            isConnected: true,
          }));
          await loadThreadMessages();
        }
      });

      matrixClient.on(sdk.RoomEvent.Timeline, async (event: any) => {
        if (event.getType() === "m.room.message") {
          await loadThreadMessages();
        }
      });
    } catch (error) {
      console.error("❌ Login error:", error);
      setChatState((prev) => ({
        ...prev,
        isLoading: false,
        error: null,
      }));
    }
  }, [
    config.matrixServer,
    config.username,
    config.password,
    loadThreadMessages,
  ]);

  // Send message
  const sendMessage = useCallback(async () => {
    const client = clientRef.current;
    if (!client || !config.roomId || !config.threadId || !newMessage.trim())
      return;

    const messageToSend = newMessage.trim();
    setNewMessage("");

    try {
      setChatState((prev) => ({ ...prev, isLoading: true }));

      await client.sendEvent(config.roomId, "m.room.message", {
        msgtype: "m.text",
        body: messageToSend,
        "m.relates_to": {
          rel_type: "m.thread",
          event_id: config.threadId,
        },
      });

      setChatState((prev) => ({ ...prev, isLoading: false }));
      await loadThreadMessages();
    } catch (error) {
      console.error("❌ Send message error:", error);
      setChatState((prev) => ({ ...prev, isLoading: false }));
      setNewMessage(messageToSend);
    }
  }, [config.roomId, config.threadId, newMessage, loadThreadMessages]);

  // Update user info
  const updateUserInfo = useCallback((updates: Partial<UserInfo>) => {
    setUserInfo((prev) => ({ ...prev, ...updates }));
  }, []);

  // Start chat
  const startChat = useCallback(async () => {
    if (!userInfo.name.trim()) return;

    setChatState((prev) => ({ ...prev, isStarted: true }));

    // Store user info in localStorage
    localStorage.setItem("chatUserInfo", JSON.stringify(userInfo));
    localStorage.setItem("roomId", config.roomId!);
    localStorage.setItem("threadId", config.threadId!);

    await handleLogin();
  }, [userInfo, config.roomId, config.threadId, handleLogin]);

  // Check localStorage on mount
  useEffect(() => {
    const savedUserInfo = localStorage.getItem("chatUserInfo");
    const savedRoomId = localStorage.getItem("roomId");
    const savedThreadId = localStorage.getItem("threadId");

    if (savedUserInfo) {
      setUserInfo(JSON.parse(savedUserInfo));
    }

    if (savedRoomId && savedThreadId) {
      setChatState((prev) => ({ ...prev, isStarted: true }));

      (async () => {
        try {
          await handleLogin();
        } catch (error) {
          setChatState((prev) => ({
            ...prev,
            error:
              error instanceof Error ? error.message : "Failed to reconnect",
          }));
        }
      })();
    }
  }, [handleLogin]);

  return {
    chatState,
    setChatState,
    userInfo,
    messages,
    newMessage,
    setNewMessage,
    containerRef,
    updateUserInfo,
    startChat,
    sendMessage,
  };
};

#!/bin/bash

# Simple Chat Widget Build Script
# This script builds the standalone chat widget in multiple formats

set -e

echo "🚀 Building Simple Chat Widget..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf dist/widget
mkdir -p dist/widget

# Build the standalone widget bundle (UMD + ES modules)
echo "📦 Building widget bundle..."
npm run build:widget-bundle

# Build the React component package
echo "⚛️ Building React component package..."
npm run build:widget-component

# Copy additional files
echo "📄 Copying additional files..."
cp WIDGET-README.md dist/widget/README.md
cp chat-widget-package.json dist/widget/package.json

# Create a simple HTML test file
echo "🧪 Creating test file..."
cat > dist/widget/test.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Chat Widget Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
        }
        .success {
            background: rgba(16, 185, 129, 0.3);
            border: 2px solid #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.3);
            border: 2px solid #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simple Chat Widget Test</h1>
        <p>This page tests the standalone chat widget build.</p>
        <div id="status" class="status">Loading...</div>
    </div>

    <script src="simple-chat-widget.umd.js"></script>
    <script>
        const statusEl = document.getElementById('status');
        
        if (window.SimpleChatWidget) {
            statusEl.textContent = '✅ Widget loaded successfully!';
            statusEl.className = 'status success';
            
            // Initialize the widget
            const widget = new SimpleChatWidget({
                primaryColor: '#667eea',
                companyName: 'Test Company',
                welcomeMessage: 'Hello! This is a test widget.',
                position: 'bottom-right'
            });
            widget.init();
        } else {
            statusEl.textContent = '❌ Failed to load widget';
            statusEl.className = 'status error';
        }
    </script>
</body>
</html>
EOF

# Create integration examples
echo "📚 Creating integration examples..."
mkdir -p dist/widget/examples

# Copy the integration examples
cp examples/widget-script-integration.html dist/widget/examples/
cp examples/widget-react-integration.tsx dist/widget/examples/

# Create a simple integration guide
cat > dist/widget/INTEGRATION.md << 'EOF'
# Simple Chat Widget Integration Guide

## Quick Start

### 1. Script Tag Integration (Easiest)

```html
<script src="simple-chat-widget.umd.js"></script>
<script>
  const widget = new SimpleChatWidget({
    primaryColor: '#3B82F6',
    companyName: 'Your Company',
    welcomeMessage: 'Hello! How can we help?'
  });
  widget.init();
</script>
```

### 2. Auto-initialization

```html
<script 
  src="simple-chat-widget.umd.js"
  data-auto-init="true"
  data-primary-color="#3B82F6"
  data-company-name="Your Company">
</script>
```

### 3. React Component (NPM)

```bash
npm install @yourcompany/simple-chat-widget
```

```jsx
import { SimpleChatWidget } from '@yourcompany/simple-chat-widget';

function App() {
  return (
    <SimpleChatWidget
      primaryColor="#3B82F6"
      companyName="Your Company"
      welcomeMessage="Hello! How can we help?"
    />
  );
}
```

## Configuration Options

- `primaryColor`: Main color for the widget
- `companyName`: Company name shown in header
- `welcomeMessage`: Welcome message text
- `position`: Widget position (bottom-right, bottom-left, top-right, top-left)
- `width`: Widget width (default: 400px)
- `height`: Widget height (default: 600px)
- `autoOpen`: Auto-open chat on load
- `showBranding`: Show "Powered by" text

See README.md for full documentation.
EOF

# Generate file sizes report
echo "📊 Generating build report..."
echo "Build Report - $(date)" > dist/widget/BUILD_REPORT.txt
echo "=================================" >> dist/widget/BUILD_REPORT.txt
echo "" >> dist/widget/BUILD_REPORT.txt

if [ -f "dist/widget/simple-chat-widget.umd.js" ]; then
    UMD_SIZE=$(wc -c < "dist/widget/simple-chat-widget.umd.js")
    echo "UMD Bundle: ${UMD_SIZE} bytes" >> dist/widget/BUILD_REPORT.txt
fi

if [ -f "dist/widget/simple-chat-widget.es.js" ]; then
    ES_SIZE=$(wc -c < "dist/widget/simple-chat-widget.es.js")
    echo "ES Module: ${ES_SIZE} bytes" >> dist/widget/BUILD_REPORT.txt
fi

if [ -f "dist/widget/simple-chat-widget.css" ]; then
    CSS_SIZE=$(wc -c < "dist/widget/simple-chat-widget.css")
    echo "CSS Bundle: ${CSS_SIZE} bytes" >> dist/widget/BUILD_REPORT.txt
fi

echo "" >> dist/widget/BUILD_REPORT.txt
echo "Files included:" >> dist/widget/BUILD_REPORT.txt
ls -la dist/widget/ >> dist/widget/BUILD_REPORT.txt

echo ""
echo "✅ Build completed successfully!"
echo ""
echo "📁 Output directory: dist/widget/"
echo "🧪 Test the build: open dist/widget/test.html in your browser"
echo "📚 Integration examples: dist/widget/examples/"
echo "📊 Build report: dist/widget/BUILD_REPORT.txt"
echo ""
echo "🚀 Your standalone chat widget is ready for deployment!"

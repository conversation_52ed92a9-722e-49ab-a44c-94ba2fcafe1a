# Simple Chat Widget

A standalone, self-contained chat widget that can be easily integrated into any website or application. Built with React and Matrix protocol for real-time messaging.

## Features

- 🚀 **Easy Integration** - Multiple integration methods (React component, Web component, Script tag)
- 🎨 **Fully Customizable** - Colors, branding, positioning, and messages
- 📱 **Responsive Design** - Works perfectly on desktop, tablet, and mobile
- 🔒 **Secure** - Built on Matrix protocol with end-to-end encryption support
- ⚡ **Lightweight** - Self-contained with no external dependencies
- 🌐 **Framework Agnostic** - Works with any framework or vanilla HTML

## Quick Start

### Method 1: Script Tag (Easiest)

Add this to your HTML:

```html
<!-- Load the widget -->
<script src="https://your-domain.com/simple-chat-widget.umd.js"></script>

<!-- Initialize with default settings -->
<script>
  const widget = new SimpleChatWidget();
  widget.init();
</script>
```

### Method 2: Auto-initialization with Data Attributes

```html
<script 
  src="https://your-domain.com/simple-chat-widget.umd.js"
  data-auto-init="true"
  data-primary-color="#FF6B6B"
  data-company-name="Your Company"
  data-welcome-message="Hello! How can we help?"
  data-position="bottom-right">
</script>
```

### Method 3: React Component (for React apps)

```bash
npm install @yourcompany/simple-chat-widget
```

```jsx
import { SimpleChatWidget } from '@yourcompany/simple-chat-widget';
import '@yourcompany/simple-chat-widget/dist/style.css';

function App() {
  return (
    <div>
      <SimpleChatWidget
        primaryColor="#FF6B6B"
        companyName="Your Company"
        welcomeMessage="Hello! How can we help?"
        onToggle={(isOpen) => console.log('Chat toggled:', isOpen)}
        onMessage={(message) => console.log('New message:', message)}
      />
    </div>
  );
}
```

### Method 4: Web Component (for any framework)

```html
<!-- Load the web component -->
<script src="https://your-domain.com/simple-chat-widget.umd.js"></script>

<!-- Use as a web component -->
<simple-chat-widget
  primary-color="#FF6B6B"
  company-name="Your Company"
  welcome-message="Hello! How can we help?">
</simple-chat-widget>
```

## Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `primaryColor` | string | `#3B82F6` | Primary color for buttons and user messages |
| `secondaryColor` | string | `#6B7280` | Secondary color for UI elements |
| `companyName` | string | `Customer Support` | Company name shown in header |
| `welcomeMessage` | string | `Hello! How can we help you today?` | Welcome message on start screen |
| `position` | string | `bottom-right` | Widget position (`bottom-right`, `bottom-left`, `top-right`, `top-left`) |
| `width` | string | `400px` | Widget width |
| `height` | string | `600px` | Widget height |
| `zIndex` | number | `9999` | CSS z-index for the widget |
| `autoOpen` | boolean | `false` | Automatically open chat on load |
| `showBranding` | boolean | `true` | Show "Powered by" branding |
| `matrixServer` | string | - | Matrix server URL |
| `username` | string | - | Matrix username |
| `password` | string | - | Matrix password |
| `roomId` | string | - | Matrix room ID |
| `threadId` | string | - | Matrix thread ID |

## Advanced Usage

### Custom Event Handling

```javascript
const widget = new SimpleChatWidget({
  primaryColor: '#FF6B6B',
  companyName: 'Your Company'
});

// Listen for widget events
window.addEventListener('simpleChatWidget:toggle', (event) => {
  console.log('Chat toggled:', event.detail.isOpen);
});

window.addEventListener('simpleChatWidget:message', (event) => {
  console.log('New message:', event.detail.message);
});

widget.init();
```

### Programmatic Control

```javascript
// Open/close the chat
widget.open();
widget.close();
widget.toggle();

// Update configuration
widget.updateConfig({
  primaryColor: '#10B981',
  companyName: 'New Company Name'
});

// Destroy the widget
widget.destroy();
```

### React Component Events

```jsx
<SimpleChatWidget
  primaryColor="#FF6B6B"
  onToggle={(isOpen) => {
    console.log('Chat toggled:', isOpen);
    // Track analytics, update state, etc.
  }}
  onMessage={(message) => {
    console.log('New message:', message);
    // Handle new messages
  }}
  onChatStarted={(userInfo) => {
    console.log('Chat started by:', userInfo);
    // Track user engagement
  }}
/>
```

## Styling and Customization

The widget comes with built-in styles that are scoped to avoid conflicts with your site's CSS. All widget styles use the `scw-` prefix.

### Custom CSS

You can override widget styles by targeting the scoped classes:

```css
/* Customize the toggle button */
.scw-toggle-btn {
  bottom: 30px !important;
  right: 30px !important;
  width: 70px !important;
  height: 70px !important;
}

/* Customize the chat window */
.scw-window {
  border-radius: 20px !important;
  box-shadow: 0 25px 70px rgba(0, 0, 0, 0.3) !important;
}

/* Customize message bubbles */
.scw-message-bubble-user {
  border-radius: 20px 20px 5px 20px !important;
}
```

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Matrix Configuration

To connect to your own Matrix server:

```javascript
const widget = new SimpleChatWidget({
  matrixServer: 'https://your-matrix-server.com',
  username: 'support-bot',
  password: 'your-password',
  roomId: '!your-room-id:your-server.com',
  threadId: '$your-thread-id'
});
```

## Security Considerations

- Always use HTTPS in production
- Keep Matrix credentials secure
- Consider using Matrix application service tokens for production
- Implement proper CORS policies on your Matrix server

## Development

```bash
# Install dependencies
npm install

# Build the widget
npm run build:widget

# Development mode
npm run dev
```

## License

MIT License - see LICENSE file for details.

## Support

For support and questions:
- GitHub Issues: [Create an issue](https://github.com/yourcompany/simple-chat-widget/issues)
- Email: <EMAIL>
- Documentation: [Full documentation](https://docs.yourcompany.com/chat-widget)

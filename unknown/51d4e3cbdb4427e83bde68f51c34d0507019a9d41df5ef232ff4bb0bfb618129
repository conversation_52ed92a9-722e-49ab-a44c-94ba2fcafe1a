import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import tailwindcss from "@tailwindcss/vite";
import { resolve } from "path";

export default defineConfig({
  plugins: [react(), tailwindcss()],
  build: {
    lib: {
      entry: resolve(__dirname, "src/widget/index.ts"),
      name: "SimpleChatWidget",
      fileName: (format) => `simple-chat-widget.${format}.js`,
      formats: ["umd", "es"],
    },
    rollupOptions: {
      external: [],
      output: {
        globals: {},
        assetFileNames: (assetInfo) => {
          if (assetInfo.name === "style.css") return "simple-chat-widget.css";
          return assetInfo.name || "";
        },
      },
    },
    outDir: "dist/widget",
    emptyOutDir: true,
    cssCodeSplit: false,
    minify: false, // Disable minification for debugging
  },
  define: {
    "process.env.NODE_ENV": '"production"',
  },
});

import React from "react";
import type { Message } from "./useStandaloneMatrixChat";

interface StandaloneChatBubbleProps {
  message: Message;
  primaryColor: string;
  showAvatar?: boolean;
  showTimestamp?: boolean;
}

export const StandaloneChatBubble: React.FC<StandaloneChatBubbleProps> = ({
  message,
  primaryColor,
  showAvatar = true,
  showTimestamp = true,
}) => {
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div
      className={`scw-message ${
        message.isCurrentUser ? "scw-message-user" : "scw-message-other"
      }`}
    >
      {/* Avatar */}
      {showAvatar && !message.isCurrentUser && (
        <div className="scw-message-avatar">
          {getInitials(message.senderName)}
        </div>
      )}

      {/* Message content */}
      <div
        className={`scw-message-content ${
          message.isCurrentUser
            ? "scw-message-content-user"
            : "scw-message-content-other"
        }`}
      >
        {/* Sender name (only for other users) */}
        {!message.isCurrentUser && (
          <span className="scw-message-sender">{message.senderName}</span>
        )}

        {/* Message bubble */}
        <div
          className={`scw-message-bubble ${
            message.isCurrentUser
              ? "scw-message-bubble-user"
              : "scw-message-bubble-other"
          }`}
          style={
            message.isCurrentUser
              ? {
                  background: `linear-gradient(to right, ${primaryColor}, ${primaryColor}dd)`,
                }
              : {}
          }
        >
          {/* Message text */}
          <p className="scw-message-text">{message.content}</p>

          {/* Media content if available */}
          {message.contentUrl && (
            <div className="scw-message-media">
              <img
                src={message.contentUrl}
                alt="Shared media"
                className="scw-message-image"
                loading="lazy"
              />
            </div>
          )}
        </div>

        {/* Timestamp */}
        {showTimestamp && (
          <span
            className={`scw-message-timestamp ${
              message.isCurrentUser
                ? "scw-message-timestamp-user"
                : "scw-message-timestamp-other"
            }`}
          >
            {formatTime(message.timestamp)}
          </span>
        )}
      </div>
    </div>
  );
};

/* Simple Chat Widget Styles */
.scw-widget {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  box-sizing: border-box;
}

.scw-widget *,
.scw-widget *::before,
.scw-widget *::after {
  box-sizing: border-box;
}

/* Toggle Button */
.scw-toggle-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: none;
  color: white;
  cursor: pointer;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scw-toggle-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.scw-toggle-btn:active {
  transform: scale(0.95);
}

/* Chat Window */
.scw-window {
  position: fixed;
  bottom: 100px;
  right: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  z-index: 9998;
  display: flex;
  flex-direction: column;
  max-width: 400px;
  max-height: 600px;
}

/* Header */
.scw-header {
  padding: 16px;
  color: white;
}

.scw-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.scw-header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.scw-avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scw-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.scw-subtitle {
  margin: 0;
  font-size: 12px;
  opacity: 0.9;
}

.scw-close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.scw-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Welcome Screen */
.scw-welcome {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.scw-welcome-content {
  flex: 1;
  padding: 32px 24px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.scw-welcome-icon {
  width: 64px;
  height: 64px;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
}

.scw-welcome-title {
  margin: 0 0 12px;
  font-size: 24px;
  font-weight: 700;
  color: #111827;
}

.scw-welcome-message {
  margin: 0 0 32px;
  color: #6b7280;
  line-height: 1.6;
}

.scw-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.scw-input {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  outline: none;
}

.scw-input:focus {
  border-color: #3b82f6;
}

.scw-start-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: opacity 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.scw-start-btn:disabled {
  cursor: not-allowed;
}

/* Chat Interface */
.scw-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.scw-messages {
  flex: 1;
  padding: 16px;
  background: #f9fafb;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.scw-messages::-webkit-scrollbar {
  width: 6px;
}

.scw-messages::-webkit-scrollbar-track {
  background: transparent;
}

.scw-messages::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.scw-messages::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Empty State */
.scw-empty-state {
  text-align: center;
  padding: 48px 16px;
}

.scw-empty-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
}

.scw-empty-title {
  margin: 0 0 8px;
  font-weight: 600;
  color: #374151;
}

.scw-empty-subtitle {
  margin: 0;
  font-size: 12px;
  color: #9ca3af;
}

/* Messages */
.scw-message {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  margin-bottom: 16px;
}

.scw-message-user {
  flex-direction: row-reverse;
}

.scw-message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.scw-message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.scw-message-content-user {
  align-items: flex-end;
}

.scw-message-sender {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  padding: 0 12px;
}

.scw-message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.scw-message-bubble-user {
  color: white;
}

.scw-message-bubble-other {
  background: #f3f4f6;
  color: #374151;
}

.scw-message-bubble-other:hover {
  background: #e5e7eb;
}

.scw-message-text {
  margin: 0;
  line-height: 1.4;
  white-space: pre-wrap;
}

.scw-message-media {
  margin-top: 8px;
}

.scw-message-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.scw-message-timestamp {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 4px;
  padding: 0 12px;
}

.scw-message-timestamp-user {
  text-align: right;
}

/* Input Area */
.scw-input-area {
  padding: 16px;
  background: white;
  border-top: 1px solid #e5e7eb;
}

.scw-input-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.scw-message-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 24px;
  outline: none;
  transition: border-color 0.2s ease;
}

.scw-message-input:focus {
  border-color: #3b82f6;
}

.scw-send-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.scw-send-btn:disabled {
  cursor: not-allowed;
}

/* Branding */
.scw-branding {
  padding: 8px 16px;
  text-align: center;
  font-size: 11px;
  color: #9ca3af;
  background: #f9fafb;
  border-top: 1px solid #f3f4f6;
}

/* Icons */
.scw-icon {
  width: 24px;
  height: 24px;
}

/* Loading Spinner */
.scw-spinner {
  display: inline-block;
}

.scw-spinner-sm {
  width: 16px;
  height: 16px;
}

.scw-spinner-md {
  width: 24px;
  height: 24px;
}

.scw-spinner-lg {
  width: 32px;
  height: 32px;
}

.scw-spinner-blue {
  color: #3b82f6;
}

.scw-spinner-white {
  color: white;
}

.scw-spinner-gray {
  color: #9ca3af;
}

.scw-spinner-svg {
  width: 100%;
  height: 100%;
  animation: scw-spin 1s linear infinite;
}

.scw-spinner-circle {
  opacity: 0.25;
}

.scw-spinner-path {
  opacity: 0.75;
}

@keyframes scw-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Animations */
.scw-message-user {
  animation: scw-slide-in-right 0.4s ease-out;
}

.scw-message-other {
  animation: scw-slide-in-left 0.4s ease-out;
}

@keyframes scw-slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scw-slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .scw-window {
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    width: 100% !important;
    height: 100% !important;
    max-width: none;
    max-height: none;
    border-radius: 0;
  }

  .scw-toggle-btn {
    bottom: 16px;
    right: 16px;
    width: 56px;
    height: 56px;
  }

  .scw-message-content {
    max-width: 85%;
  }
}

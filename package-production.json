{"name": "@yourcompany/messenger-chat-widget", "version": "1.0.0", "description": "A beautiful, responsive Messenger-style chat widget for React applications with Matrix integration", "main": "dist/index.js", "module": "dist/index.esm.js", "umd": "dist/index.umd.js", "types": "dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/types/index.d.ts"}, "./styles": "./dist/styles/chat-widget.css", "./package.json": "./package.json"}, "files": ["dist", "README.md", "LICENSE", "CHANGELOG.md"], "scripts": {"build": "rollup -c", "build:watch": "rollup -c -w", "build:types": "tsc --project tsconfig.build.json --emitDeclarationOnly", "dev": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build && npm run test", "prepack": "npm run build", "size": "size-limit", "analyze": "npm run build && npx bundle-analyzer dist/index.esm.js"}, "keywords": ["react", "chat", "widget", "messenger", "matrix", "customer-support", "live-chat", "typescript", "responsive", "accessible"], "author": "Your Company <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourcompany/messenger-chat-widget.git"}, "bugs": {"url": "https://github.com/yourcompany/messenger-chat-widget/issues"}, "homepage": "https://github.com/yourcompany/messenger-chat-widget#readme", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "peerDependenciesMeta": {"react": {"optional": false}, "react-dom": {"optional": false}}, "dependencies": {"matrix-js-sdk": "^37.5.0", "axios": "^1.6.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.5", "@size-limit/preset-small-lib": "^11.0.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "rimraf": "^5.0.5", "rollup": "^4.6.1", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "size-limit": "^11.0.1", "typescript": "^5.3.2"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "size-limit": [{"path": "dist/index.esm.js", "limit": "50 KB"}, {"path": "dist/index.umd.js", "limit": "60 KB"}]}
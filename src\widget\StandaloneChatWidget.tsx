import React, { useState, useEffect, useCallback } from "react";
import type { ChatWidgetConfig } from "./useStandaloneMatrixChat";
import { useStandaloneMatrixChat } from "./useStandaloneMatrixChat";
import { StandaloneChatBubble } from "./StandaloneChatBubble";
import { LoadingSpinner } from "./LoadingSpinner";

interface StandaloneChatWidgetProps {
  config: ChatWidgetConfig;
  onToggle?: (isOpen: boolean) => void;
  onMessage?: (message: any) => void;
}

export const StandaloneChatWidget: React.FC<StandaloneChatWidgetProps> = ({
  config,
  onToggle,
  onMessage,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const {
    chatState,
    userInfo,
    messages,
    newMessage,
    setNewMessage,
    containerRef,
    updateUserInfo,
    startChat,
    sendMessage,
  } = useStandaloneMatrixChat(config);

  // Handle external events
  useEffect(() => {
    const handleOpen = () => setIsOpen(true);
    const handleClose = () => setIsOpen(false);
    const handleToggle = () => setIsOpen((prev) => !prev);

    window.addEventListener("simpleChatWidget:open", handleOpen);
    window.addEventListener("simpleChatWidget:close", handleClose);
    window.addEventListener("simpleChatWidget:toggle", handleToggle);

    return () => {
      window.removeEventListener("simpleChatWidget:open", handleOpen);
      window.removeEventListener("simpleChatWidget:close", handleClose);
      window.removeEventListener("simpleChatWidget:toggle", handleToggle);
    };
  }, []);

  const handleToggleChat = useCallback(() => {
    const newIsOpen = !isOpen;
    setIsOpen(newIsOpen);
    onToggle?.(newIsOpen);
  }, [isOpen, onToggle]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (chatState.isStarted) {
        sendMessage();
      } else if (userInfo.name.trim()) {
        startChat();
      }
    }
  };

  const isFormValid = userInfo.name.trim().length > 0;

  return (
    <div className="scw-widget">
      {/* Chat toggle button */}
      <button
        onClick={handleToggleChat}
        className="scw-toggle-btn"
        style={{
          background: `linear-gradient(to right, ${config.primaryColor}, ${config.primaryColor}dd)`,
        }}
        aria-label="Toggle chat support"
      >
        {isOpen ? (
          <svg
            className="scw-icon"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        ) : (
          <svg className="scw-icon" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
              clipRule="evenodd"
            />
          </svg>
        )}
      </button>

      {/* Chat window */}
      {isOpen && (
        <div
          className="scw-window"
          style={{ width: config.width, height: config.height }}
        >
          {!chatState.isStarted ? (
            /* Welcome form */
            <div className="scw-welcome">
              {/* Header */}
              <div
                className="scw-header"
                style={{
                  background: `linear-gradient(to right, ${config.primaryColor}, ${config.primaryColor}dd)`,
                }}
              >
                <div className="scw-header-content">
                  <div className="scw-header-info">
                    <div className="scw-avatar">
                      <svg
                        className="scw-icon"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="scw-title">{config.companyName}</h3>
                      <p className="scw-subtitle">Online now</p>
                    </div>
                  </div>
                  <button onClick={handleToggleChat} className="scw-close-btn">
                    <svg
                      className="scw-icon"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Welcome content */}
              <div className="scw-welcome-content">
                <div className="scw-welcome-icon">
                  <svg
                    className="scw-icon"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <h2 className="scw-welcome-title">Welcome!</h2>
                <p className="scw-welcome-message">{config.welcomeMessage}</p>

                <div className="scw-form">
                  <input
                    type="text"
                    placeholder="Your name"
                    value={userInfo.name}
                    onChange={(e) => updateUserInfo({ name: e.target.value })}
                    onKeyDown={handleKeyPress}
                    className="scw-input"
                    style={{
                      borderColor: isFormValid
                        ? config.primaryColor
                        : undefined,
                    }}
                  />
                  <input
                    type="email"
                    placeholder="Email (optional)"
                    value={userInfo.email}
                    onChange={(e) => updateUserInfo({ email: e.target.value })}
                    onKeyDown={handleKeyPress}
                    className="scw-input"
                  />
                  <button
                    onClick={startChat}
                    disabled={!isFormValid || chatState.isLoading}
                    className="scw-start-btn"
                    style={{
                      backgroundColor: config.primaryColor,
                      opacity: !isFormValid || chatState.isLoading ? 0.5 : 1,
                    }}
                  >
                    {chatState.isLoading ? (
                      <LoadingSpinner size="sm" color="white" />
                    ) : (
                      "Start Chat"
                    )}
                  </button>
                </div>
              </div>
            </div>
          ) : (
            /* Chat interface */
            <div className="scw-chat">
              {/* Header */}
              <div
                className="scw-header"
                style={{
                  background: `linear-gradient(to right, ${config.primaryColor}, ${config.primaryColor}dd)`,
                }}
              >
                <div className="scw-header-content">
                  <div className="scw-header-info">
                    <div className="scw-avatar">
                      <svg
                        className="scw-icon"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="scw-title">{config.companyName}</h3>
                      <p className="scw-subtitle">
                        {chatState.isConnected ? "Online" : "Connecting..."}
                      </p>
                    </div>
                  </div>
                  <button onClick={handleToggleChat} className="scw-close-btn">
                    <svg
                      className="scw-icon"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Messages */}
              <div ref={containerRef} className="scw-messages">
                {messages.length === 0 ? (
                  <div className="scw-empty-state">
                    <div
                      className="scw-empty-icon"
                      style={{ backgroundColor: `${config.primaryColor}20` }}
                    >
                      <svg
                        className="scw-icon"
                        style={{ color: config.primaryColor }}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <p className="scw-empty-title">Welcome to support!</p>
                    <p className="scw-empty-subtitle">
                      Send a message to start the conversation
                    </p>
                  </div>
                ) : (
                  messages.map((msg) => (
                    <StandaloneChatBubble
                      key={msg.eventId}
                      message={msg}
                      primaryColor={config.primaryColor!}
                    />
                  ))
                )}
              </div>

              {/* Input */}
              <div className="scw-input-area">
                <div className="scw-input-container">
                  <input
                    type="text"
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={handleKeyPress}
                    disabled={!chatState.isConnected || chatState.isLoading}
                    className="scw-message-input"
                    style={{
                      borderColor: newMessage.trim()
                        ? config.primaryColor
                        : undefined,
                    }}
                  />
                  <button
                    onClick={sendMessage}
                    disabled={
                      !newMessage.trim() ||
                      !chatState.isConnected ||
                      chatState.isLoading
                    }
                    className="scw-send-btn"
                    style={{
                      backgroundColor: config.primaryColor,
                      opacity:
                        !newMessage.trim() ||
                        !chatState.isConnected ||
                        chatState.isLoading
                          ? 0.5
                          : 1,
                    }}
                  >
                    {chatState.isLoading ? (
                      <LoadingSpinner size="sm" color="white" />
                    ) : (
                      <svg
                        className="scw-icon"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                        />
                      </svg>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Branding */}
          {config.showBranding && (
            <div className="scw-branding">
              <span>Powered by SimpleChatWidget</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

import React, { useEffect, useState } from "react";
import EmbeddableChatWindow from "../components/chat/EmbeddableChatWindow";

const EmbedPage: React.FC = () => {
  const [config, setConfig] = useState({
    primaryColor: "#3B82F6",
    companyName: "Hỗ trợ khách hàng",
    welcomeMessage:
      "Chúng tôi sẵn sàng hỗ trợ bạn. Vui lòng cho biết tên để bắt đầu cuộc trò chuyện.",
  });

  useEffect(() => {
    // Apply iframe-specific styles to body
    document.body.classList.add("iframe-mode");
    document.documentElement.classList.add("embed-transparent");

    // Parse URL parameters for configuration
    const urlParams = new URLSearchParams(window.location.search);
    const newConfig = { ...config };

    // Extract configuration from URL parameters
    if (urlParams.get("primaryColor")) {
      newConfig.primaryColor = `#${urlParams
        .get("primaryColor")
        ?.replace("#", "")}`;
    }

    if (urlParams.get("companyName")) {
      newConfig.companyName = decodeURIComponent(
        urlParams.get("companyName") || ""
      );
    }
    if (urlParams.get("welcomeMessage")) {
      newConfig.welcomeMessage = decodeURIComponent(
        urlParams.get("welcomeMessage") || ""
      );
    }

    setConfig(newConfig);

    // Set up iframe communication
    const handleMessage = (event: MessageEvent) => {
      // Only accept messages from trusted origins
      const trustedOrigins = [
        "http://localhost:3000",
        "https://livechat.dev.longvan.vn",
        "https://livechat.longvan.vn",
      ];

      if (!trustedOrigins.some((origin) => event.origin.startsWith(origin))) {
        return;
      }

      if (event.data.type === "CHAT_CONFIG_UPDATE") {
        setConfig((prevConfig) => ({
          ...prevConfig,
          ...event.data.config,
        }));
      }
    };

    window.addEventListener("message", handleMessage);

    // Send ready message to parent
    window.parent.postMessage(
      {
        source: "longvan-chat",
        type: "IFRAME_READY",
        data: { timestamp: Date.now() },
      },
      "*"
    );

    return () => {
      window.removeEventListener("message", handleMessage);
      // Cleanup iframe-specific styles
      document.body.classList.remove("iframe-mode");
      document.documentElement.classList.remove("embed-transparent");
    };
  }, []);

  // Send chat events to parent window
  useEffect(() => {
    const sendChatEvent = (type: string, data?: any) => {
      window.parent.postMessage(
        {
          source: "longvan-chat",
          type,
          data,
        },
        "*"
      );
    };

    // Listen for chat state changes and notify parent
    const handleChatStateChange = () => {
      sendChatEvent("CHAT_STATE_CHANGED", {
        timestamp: Date.now(),
      });
    };

    // Set up event listeners for chat interactions
    document.addEventListener("click", handleChatStateChange);
    document.addEventListener("keydown", handleChatStateChange);

    return () => {
      document.removeEventListener("click", handleChatStateChange);
      document.removeEventListener("keydown", handleChatStateChange);
    };
  }, []);

  return (
    <div
      style={{
        position: "relative",
      }}
    >
      <EmbeddableChatWindow
        primaryColor={config.primaryColor}
        companyName={config.companyName}
        welcomeMessage={config.welcomeMessage}
      />
    </div>
  );
};

export default EmbedPage;

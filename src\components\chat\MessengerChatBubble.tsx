import React from "react";
import { Message } from "../../hooks/useMatrixChat";

interface MessengerChatBubbleProps {
  message: Message;
  showAvatar?: boolean;
  showTimestamp?: boolean;
}

const MessengerChatBubble: React.FC<MessengerChatBubbleProps> = ({
  message,
  showAvatar = true,
  showTimestamp = true,
}) => {
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString("vi-VN", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div
      className={`flex items-end space-x-2 mb-4 ${
        message.isCurrentUser ? "flex-row-reverse space-x-reverse" : ""
      } ${message.isCurrentUser ? "chat-bubble-user" : "chat-bubble-other"}`}
    >
      {/* Avatar */}
      {showAvatar && !message.isCurrentUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-xs font-semibold shadow-md">
          {getInitials(message.senderName)}
        </div>
      )}

      {/* Message content */}
      <div
        className={`flex flex-col max-w-xs ${
          message.isCurrentUser ? "items-end" : "items-start"
        }`}
      >
        {/* Sender name (only for other users) */}
        {!message.isCurrentUser && (
          <span className="text-xs text-gray-500 mb-1 px-3">
            {message.senderName}
          </span>
        )}

        {/* Timestamp */}
        {showTimestamp && (
          <span
            className={`text-xs text-gray-400 mt-1 px-3 ${
              message.isCurrentUser ? "text-right" : "text-left"
            }`}
          >
            {formatTime(message.timestamp)}
          </span>
        )}
        {/* Message bubble */}
        <div
          className={`relative px-4 py-3 rounded-2xl max-w-xs break-words shadow-sm transition-all duration-200 ${
            message.isCurrentUser
              ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white"
              : "bg-gray-100 text-gray-800 hover:bg-gray-200"
          }`}
        >
          {/* Message text */}
          <p className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </p>

          {/* Media content if available */}
          {message.contentUrl && (
            <div className="mt-2">
              <img
                src={message.contentUrl}
                alt="Shared media"
                className="max-w-full h-auto rounded-lg"
                loading="lazy"
              />
            </div>
          )}
        </div>
      </div>

      {/* Current user avatar (right side) */}
      {showAvatar && message.isCurrentUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center text-white shadow-md">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default MessengerChatBubble;

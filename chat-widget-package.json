{"name": "@yourcompany/simple-chat-widget", "version": "1.0.0", "description": "Standalone, self-contained chat widget that can be easily integrated into any website or application", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "unpkg": "dist/simple-chat-widget.umd.js", "jsdelivr": "dist/simple-chat-widget.umd.js", "files": ["dist", "README.md", "LICENSE"], "scripts": {"build": "rollup -c", "build:watch": "rollup -c -w", "dev": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "prepublishOnly": "npm run build"}, "keywords": ["react", "chat", "widget", "customer-support", "live-chat", "matrix", "standalone", "self-contained", "embeddable", "typescript", "vanilla-js", "web-component"], "author": "Your Company <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourcompany/simple-chat-widget.git"}, "bugs": {"url": "https://github.com/yourcompany/simple-chat-widget/issues"}, "homepage": "https://github.com/yourcompany/simple-chat-widget#readme", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"matrix-js-sdk": "^37.5.0", "axios": "^1.6.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.0.0", "rollup": "^4.0.0", "@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "typescript": "^5.0.0"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}
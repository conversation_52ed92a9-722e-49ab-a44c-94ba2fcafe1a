#!/bin/bash

# Build script for messenger chat widget
set -e

echo "🏗️  Building Messenger Chat Widget..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf dist

# Type checking
echo "🔍 Type checking..."
npx tsc --noEmit

# Linting
echo "🔍 Linting..."
npm run lint

# Run tests
echo "🧪 Running tests..."
npm run test

# Build with Rollup
echo "📦 Building with Rollup..."
npx rollup -c

# Generate TypeScript declarations
echo "📝 Generating TypeScript declarations..."
npx tsc --project tsconfig.build.json --emitDeclarationOnly

# Copy additional files
echo "📋 Copying additional files..."
cp README.md dist/
cp LICENSE dist/
cp CHANGELOG.md dist/ 2>/dev/null || echo "⚠️  CHANGELOG.md not found, skipping..."

# Verify build
echo "✅ Verifying build..."
if [ -f "dist/index.js" ] && [ -f "dist/index.esm.js" ] && [ -f "dist/index.umd.js" ]; then
    echo "✅ All build outputs generated successfully!"
else
    echo "❌ Build verification failed!"
    exit 1
fi

# Check bundle size
echo "📊 Checking bundle size..."
npx size-limit

echo "🎉 Build completed successfully!"
echo ""
echo "📦 Generated files:"
echo "  - dist/index.js (CommonJS)"
echo "  - dist/index.esm.js (ES Module)"
echo "  - dist/index.umd.js (UMD)"
echo "  - dist/types/index.d.ts (TypeScript declarations)"
echo "  - dist/styles/chat-widget.css (Styles)"

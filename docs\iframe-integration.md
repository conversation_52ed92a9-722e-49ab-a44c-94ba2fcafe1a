# Iframe-Based Chat Widget Integration Guide

This guide explains how to integrate the Longvan Chat Widget into your website using iframe embedding for maximum compatibility and isolation.

## Quick Start

### Basic Integration

Add this code to your HTML page where you want the chat widget to appear:

```html
<script src="https://livechat.dev.longvan.vn/embed.js"></script>
<script>
  LongvanChat.init();
</script>
```

### Custom Configuration

```html
<script src="https://livechat.dev.longvan.vn/embed.js"></script>
<script>
  LongvanChat.init({
    primaryColor: "#FF6B6B",
    companyName: "Your Company",
    welcomeMessage: "Hello! How can we help you today?",
    position: "bottom-right",
    transparent: true,
    responsive: true,
  });
</script>
```

## Configuration Options

### Core Appearance

| Option           | Type   | Default                                    | Description                           |
| ---------------- | ------ | ------------------------------------------ | ------------------------------------- |
| `primaryColor`   | string | `#3B82F6`                                  | Primary color for buttons and accents |
| `secondaryColor` | string | `#6B7280`                                  | Secondary color for text and borders  |
| `companyName`    | string | `Hỗ trợ khách hàng`                        | Company name displayed in header      |
| `welcomeMessage` | string | `Xin chào! Chúng tôi sẵn sàng hỗ trợ bạn.` | Welcome message shown to users        |

### Layout & Positioning

| Option     | Type   | Default        | Description                                                      |
| ---------- | ------ | -------------- | ---------------------------------------------------------------- |
| `width`    | string | `400px`        | Widget width (px, %, vw)                                         |
| `height`   | string | `600px`        | Widget height (px, %, vh)                                        |
| `position` | string | `bottom-right` | Position: `bottom-right`, `bottom-left`, `top-right`, `top-left` |
| `zIndex`   | number | `9999`         | CSS z-index for layering                                         |

### Advanced Options

| Option         | Type    | Default | Description                                        |
| -------------- | ------- | ------- | -------------------------------------------------- |
| `transparent`  | boolean | `false` | Enable transparent background                      |
| `responsive`   | boolean | `true`  | Enable responsive design                           |
| `clickThrough` | boolean | `false` | Allow clicks to pass through non-interactive areas |
| `baseUrl`      | string  | `auto`  | Custom base URL for the chat endpoint              |

## Integration Methods

### 1. JavaScript API (Recommended)

```html
<script src="https://livechat.dev.longvan.vn/embed.js"></script>
<script>
  LongvanChat.init({
    primaryColor: "#FF6B6B",
    companyName: "Your Company",
  });
</script>
```

### 2. Auto-initialization with Data Attributes

```html
<script
  src="https://livechat.dev.longvan.vn/embed.js"
  data-auto-init="true"
  data-primary-color="#FF6B6B"
  data-company-name="Your Company"
  data-position="bottom-right"
></script>
```

### 3. Direct Iframe Embedding

```html
<iframe
  src="https://livechat.dev.longvan.vn/embed?primaryColor=FF6B6B&companyName=Your%20Company"
  width="400"
  height="600"
  frameborder="0"
  style="border-radius: 12px; box-shadow: 0 10px 25px rgba(0,0,0,0.1);"
>
</iframe>
```

## Responsive Design

The widget automatically adapts to different screen sizes:

### Desktop (>768px)

- Full-featured interface
- 400px default width
- Hover effects enabled

### Tablet (768px - 480px)

- Optimized touch targets
- Adjusted font sizes
- Responsive width

### Mobile (<480px)

- Compact interface
- Touch-optimized controls
- Full-width on small screens

## Customization Examples

### E-commerce Store

```javascript
LongvanChat.init({
  primaryColor: "#10B981",
  companyName: "ShopName Support",
  welcomeMessage: "Need help with your order? We're here to assist!",
  position: "bottom-right",
  height: "500px",
});
```

### SaaS Application

```javascript
LongvanChat.init({
  primaryColor: "#8B5CF6",
  companyName: "Technical Support",
  welcomeMessage: "Having technical issues? Let our experts help!",
  transparent: false,
  responsive: true,
});
```

### Minimal Integration

```javascript
LongvanChat.init({
  transparent: true,
  clickThrough: true,
  width: "300px",
  height: "400px",
});
```

## Event Handling

Listen for chat widget events:

```javascript
// Initialize with event handlers
LongvanChat.init({
  primaryColor: "#FF6B6B",
  onIframeReady: function (data) {
    console.log("Chat widget loaded");
  },
  onChatStarted: function (data) {
    console.log("User started chat:", data);
  },
  onChatOpened: function (data) {
    console.log("Chat window opened");
  },
  onChatClosed: function (data) {
    console.log("Chat window closed");
  },
});

// Or listen for global events
window.addEventListener("message", function (event) {
  if (event.data.source === "longvan-chat") {
    console.log("Chat event:", event.data.type, event.data.data);
  }
});
```

## API Methods

### Control Methods

```javascript
// Show the chat widget
LongvanChat.show();

// Hide the chat widget
LongvanChat.hide();

// Destroy the chat widget
LongvanChat.destroy();

// Update configuration
LongvanChat.updateConfig({
  primaryColor: "#FF0000",
});
```

### Information Methods

```javascript
// Check if widget is visible
const isVisible = LongvanChat.isVisible();

// Get current configuration
const config = LongvanChat.getConfig();

// Get widget status
const status = LongvanChat.getStatus();
```

## Troubleshooting

### Common Issues

1. **Widget not appearing**

   - Check console for JavaScript errors
   - Verify the embed script URL is accessible
   - Ensure no ad blockers are interfering

2. **Styling conflicts**

   - Use `transparent: true` for seamless integration
   - Adjust `zIndex` if widget appears behind other elements
   - Check for CSS conflicts with your site's styles

3. **Mobile responsiveness**
   - Enable `responsive: true` (default)
   - Test on various device sizes
   - Consider adjusting `width` and `height` for mobile

### Debug Mode

Enable debug logging:

```javascript
LongvanChat.init({
  debug: true,
  primaryColor: "#FF6B6B",
});
```

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Security Considerations

- The widget runs in an isolated iframe for security
- All communication uses postMessage API
- No access to parent page data
- HTTPS required for production use

## Performance

- Lazy loading: Widget loads only when needed
- Minimal bundle size: ~50KB gzipped
- CDN delivery for fast loading
- Optimized for mobile networks

## Next Steps

1. Test the integration in your development environment
2. Customize the appearance to match your brand
3. Set up event tracking for analytics
4. Deploy to production with HTTPS
5. Monitor performance and user feedback

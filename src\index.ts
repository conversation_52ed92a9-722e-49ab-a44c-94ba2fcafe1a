// Main exports for the chat widget package
export { default as ChatWidget } from './components/chat/ChatWidget';
export { default as Simple<PERSON>hatWindow } from './components/chat/SimpleChatWindow';
export { default as MessengerChatBubble } from './components/chat/MessengerChatBubble';
export { default as LoadingSpinner } from './components/ui/LoadingSpinner';

// Hooks
export { useMatrixChat } from './hooks/useMatrixChat';

// Theme provider
export { ChatThemeProvider, useChatTheme } from './components/chat/ChatThemeProvider';

// Types
export type { ChatWidgetConfig } from './components/chat/ChatWidget';
export type { ChatTheme } from './components/chat/ChatThemeProvider';
export type { Message, ChatState, UserInfo } from './hooks/useMatrixChat';

// Styles (import this in your main CSS file)
import './styles/chat-widget.css';

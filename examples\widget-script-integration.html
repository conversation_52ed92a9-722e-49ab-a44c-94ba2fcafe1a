<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Chat Widget - Script Integration</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .hero {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            margin-bottom: 40px;
        }
        
        .demo-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .demo-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 16px;
        }
        
        .demo-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(16, 185, 129, 0.3);
            border: 2px solid #10b981;
        }
        
        .status.error {
            background: rgba(239, 68, 68, 0.3);
            border: 2px solid #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1>🚀 Simple Chat Widget Demo</h1>
            <p>
                Experience our standalone chat widget with script tag integration.
                No framework required - just add the script and you're ready to go!
            </p>
        </div>
        
        <div class="content">
            <h2>Widget Status</h2>
            <div id="widgetStatus" class="status">Loading...</div>
            
            <h2>🎮 Try Different Configurations</h2>
            <div class="demo-buttons">
                <button class="demo-button" onclick="loadDefault()">
                    Default Theme
                </button>
                <button class="demo-button" onclick="loadRedTheme()">
                    Red Theme
                </button>
                <button class="demo-button" onclick="loadGreenTheme()">
                    Green Theme
                </button>
                <button class="demo-button" onclick="loadPurpleTheme()">
                    Purple Theme
                </button>
                <button class="demo-button" onclick="toggleWidget()">
                    Toggle Chat
                </button>
                <button class="demo-button" onclick="destroyWidget()">
                    Destroy Widget
                </button>
            </div>
        </div>
        
        <div class="content">
            <h2>📋 Integration Code</h2>
            <p>This page demonstrates script tag integration. Here's the code:</p>
            
            <h3>Basic Integration:</h3>
            <div class="code-block">
&lt;!-- Load the widget script --&gt;
&lt;script src="dist/widget/simple-chat-widget.umd.js"&gt;&lt;/script&gt;

&lt;!-- Initialize the widget --&gt;
&lt;script&gt;
  const widget = new SimpleChatWidget({
    primaryColor: '#667eea',
    companyName: 'Demo Company',
    welcomeMessage: 'Hello! How can we help you today?'
  });
  widget.init();
&lt;/script&gt;
            </div>
            
            <h3>Auto-initialization with Data Attributes:</h3>
            <div class="code-block">
&lt;script 
  src="dist/widget/simple-chat-widget.umd.js"
  data-auto-init="true"
  data-primary-color="#667eea"
  data-company-name="Demo Company"
  data-welcome-message="Hello! How can we help?"
  data-position="bottom-right"&gt;
&lt;/script&gt;
            </div>
        </div>
        
        <div class="content">
            <h2>🔧 Event Log</h2>
            <div id="eventLog" style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
                <div>Event log will appear here...</div>
            </div>
        </div>
    </div>

    <!-- Load the Simple Chat Widget -->
    <script src="../dist/widget/simple-chat-widget.umd.js"></script>
    
    <script>
        let currentWidget = null;
        let eventLogContainer = document.getElementById('eventLog');
        let statusContainer = document.getElementById('widgetStatus');
        
        function logEvent(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            eventLogContainer.appendChild(logEntry);
            eventLogContainer.scrollTop = eventLogContainer.scrollHeight;
        }
        
        function updateStatus(message, isSuccess = true) {
            statusContainer.textContent = message;
            statusContainer.className = `status ${isSuccess ? 'success' : 'error'}`;
        }
        
        // Check if SimpleChatWidget is loaded
        if (window.SimpleChatWidget) {
            updateStatus('✅ Simple Chat Widget loaded successfully!', true);
            logEvent('SimpleChatWidget class is available');
        } else {
            updateStatus('❌ Failed to load Simple Chat Widget', false);
            logEvent('SimpleChatWidget class not found');
        }
        
        // Listen for widget events
        window.addEventListener('simpleChatWidget:toggle', function(event) {
            logEvent(`Chat toggled: ${event.detail.isOpen ? 'opened' : 'closed'}`);
        });
        
        window.addEventListener('simpleChatWidget:message', function(event) {
            logEvent(`New message: ${JSON.stringify(event.detail.message)}`);
        });
        
        // Demo functions
        function loadDefault() {
            destroyWidget();
            currentWidget = new SimpleChatWidget({
                primaryColor: '#3B82F6',
                companyName: 'Demo Company',
                welcomeMessage: 'Hello! How can we help you today?',
                position: 'bottom-right'
            });
            currentWidget.init();
            logEvent('Loaded default theme');
        }
        
        function loadRedTheme() {
            destroyWidget();
            currentWidget = new SimpleChatWidget({
                primaryColor: '#EF4444',
                companyName: 'Red Support Team',
                welcomeMessage: 'Welcome to our red-themed support!',
                position: 'bottom-right'
            });
            currentWidget.init();
            logEvent('Loaded red theme');
        }
        
        function loadGreenTheme() {
            destroyWidget();
            currentWidget = new SimpleChatWidget({
                primaryColor: '#10B981',
                companyName: 'Green Support Team',
                welcomeMessage: 'Eco-friendly support team here!',
                position: 'bottom-left'
            });
            currentWidget.init();
            logEvent('Loaded green theme');
        }
        
        function loadPurpleTheme() {
            destroyWidget();
            currentWidget = new SimpleChatWidget({
                primaryColor: '#8B5CF6',
                companyName: 'Purple Premium Support',
                welcomeMessage: 'Premium support experience awaits!',
                position: 'top-right'
            });
            currentWidget.init();
            logEvent('Loaded purple theme');
        }
        
        function toggleWidget() {
            if (currentWidget) {
                currentWidget.toggle();
                logEvent('Toggled widget');
            } else {
                logEvent('No widget to toggle');
            }
        }
        
        function destroyWidget() {
            if (currentWidget) {
                currentWidget.destroy();
                currentWidget = null;
                logEvent('Widget destroyed');
            }
        }
        
        // Auto-initialize with default settings
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (window.SimpleChatWidget) {
                    loadDefault();
                }
            }, 1000);
        });
    </script>
</body>
</html>

# 🚀 Iframe-Based Chat Widget Integration

This document provides a comprehensive guide for integrating the SimpleChatWindow component as an embeddable iframe widget into any website or web application.

## ✨ Features

- **🎨 Fully Customizable**: Colors, branding, messages, and layout
- **📱 Responsive Design**: Adapts to all screen sizes and devices
- **🔒 Secure Iframe Isolation**: Runs in isolated environment for security
- **⚡ Lightweight**: Minimal performance impact on host pages
- **🌐 Cross-Origin Support**: Works across different domains
- **🎯 Click-Through Support**: Optional transparent areas for seamless integration
- **📊 Event Communication**: Real-time communication between iframe and parent
- **♿ Accessibility**: Full keyboard navigation and screen reader support

## 🏗️ Implementation Overview

### New Components Created

1. **`EmbeddableChatWindow.tsx`** - Iframe-optimized version of SimpleChatWindow
2. **`EmbedPage.tsx`** - Dedicated page serving only the chat component
3. **`embed.css`** - Iframe-specific styles for transparency and responsiveness
4. **Enhanced `embed.js`** - Updated embed script with new configuration options

### New Routes

- **`/embed`** - Dedicated endpoint serving the embeddable chat widget

## 🚀 Quick Start

### 1. Basic Integration

```html
<!-- Add to your website -->
<script src="http://localhost:3000/embed.js"></script>
<script>
  LongvanChat.init();
</script>
```

### 2. Custom Configuration

```html
<script src="http://localhost:3000/embed.js"></script>
<script>
  LongvanChat.init({
    primaryColor: "#FF6B6B",
    companyName: "Your Company",
    welcomeMessage: "Hello! How can we help?",
    position: "bottom-right",
    transparent: true,
    responsive: true,
  });
</script>
```

### 3. Direct Iframe Embedding

```html
<iframe
  src="http://localhost:3000/embed?primaryColor=FF6B6B&companyName=Your%20Company"
  width="400"
  height="600"
  frameborder="0"
  style="border-radius: 12px;"
>
</iframe>
```

## ⚙️ Configuration Options

| Option           | Type    | Default                                    | Description                             |
| ---------------- | ------- | ------------------------------------------ | --------------------------------------- |
| `primaryColor`   | string  | `#3B82F6`                                  | Primary brand color                     |
| `secondaryColor` | string  | `#6B7280`                                  | Secondary color                         |
| `companyName`    | string  | `Hỗ trợ khách hàng`                        | Company name in header                  |
| `welcomeMessage` | string  | `Xin chào! Chúng tôi sẵn sàng hỗ trợ bạn.` | Welcome message                         |
| `width`          | string  | `400px`                                    | Widget width                            |
| `height`         | string  | `600px`                                    | Widget height                           |
| `position`       | string  | `bottom-right`                             | Position on page                        |
| `transparent`    | boolean | `false`                                    | Transparent background                  |
| `responsive`     | boolean | `true`                                     | Responsive design                       |
| `clickThrough`   | boolean | `false`                                    | Click-through for non-interactive areas |

## 📱 Responsive Behavior

### Desktop (>768px)

- Fixed width floating widget
- Hover effects and animations
- Full feature set

### Tablet (768px - 480px)

- Adaptive width
- Touch-optimized controls
- Adjusted spacing

### Mobile (<480px)

- Can expand to full screen
- Touch-friendly interface
- Optimized for small screens

## 🎨 Customization Examples

### E-commerce Theme

```javascript
LongvanChat.init({
  primaryColor: "#10B981",
  companyName: "ShopName Support",
  welcomeMessage: "Need help with your order?",
  height: "500px",
});
```

### SaaS Application

```javascript
LongvanChat.init({
  primaryColor: "#8B5CF6",
  companyName: "Technical Support",
  transparent: false,
  position: "bottom-left",
});
```

### Minimal Integration

```javascript
LongvanChat.init({
  transparent: true,
  clickThrough: true,
  width: "300px",
  height: "400px",
});
```

## 🔧 API Methods

```javascript
// Control methods
LongvanChat.show(); // Show widget
LongvanChat.hide(); // Hide widget
LongvanChat.destroy(); // Remove widget
LongvanChat.updateConfig({
  // Update configuration
  primaryColor: "#FF0000",
});

// Information methods
LongvanChat.isVisible(); // Check visibility
LongvanChat.getConfig(); // Get current config
LongvanChat.getStatus(); // Get widget status
```

## 📊 Event Handling

```javascript
// Listen for chat events
window.addEventListener("message", function (event) {
  if (event.data.source === "longvan-chat") {
    console.log("Event:", event.data.type, event.data.data);
  }
});

// Available events:
// - IFRAME_READY
// - CHAT_STATE_CHANGED
// - CHAT_OPENED
// - CHAT_CLOSED
// - MESSAGE_SENT
// - MESSAGE_RECEIVED
```

## 📁 File Structure

```
src/
├── components/chat/
│   ├── EmbeddableChatWindow.tsx    # Iframe-optimized chat component
│   └── SimpleChatWindow.tsx        # Original chat component
├── pages/
│   ├── EmbedPage.tsx              # Dedicated embed endpoint
│   └── HomePage.tsx               # Main application page
├── styles/
│   └── embed.css                  # Iframe-specific styles
public/
├── embed.js                       # Enhanced embed script
└── embed-test.html               # Test page for embed script
examples/
├── basic-integration.html         # Basic integration example
├── advanced-integration.html      # Advanced features demo
└── responsive-integration.html    # Responsive design demo
docs/
└── iframe-integration.md         # Detailed integration guide
```

## 🧪 Testing

### Local Testing

1. Start the development server: `npm run dev`
2. Open test pages:
   - Basic: `http://localhost:3000/examples/basic-integration.html`
   - Advanced: `http://localhost:3000/examples/advanced-integration.html`
   - Responsive: `http://localhost:3000/examples/responsive-integration.html`

### Integration Testing

1. Test the embed endpoint: `http://localhost:3000/embed`
2. Test with parameters: `http://localhost:3000/embed?primaryColor=FF6B6B&companyName=Test`
3. Test the embed script: `http://localhost:3000/embed-test.html`

## 🚀 Deployment

### Production Setup

1. Build the application: `npm run build`
2. Deploy to your hosting platform
3. Update embed script URLs to production domain
4. Test cross-origin functionality

### CDN Integration

```html
<!-- Production embed script -->
<script src="https://your-domain.com/embed.js"></script>
<script>
  LongvanChat.init({
    primaryColor: "#your-brand-color",
  });
</script>
```

## 🔒 Security Considerations

- Iframe isolation prevents access to parent page data
- PostMessage API used for secure communication
- HTTPS required for production deployment
- Content Security Policy (CSP) compatible

## 🌐 Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📈 Performance

- Lazy loading: Widget loads only when needed
- Minimal bundle size: ~50KB gzipped
- CDN-ready for global distribution
- Optimized for mobile networks

## 🆘 Troubleshooting

### Common Issues

1. **Widget not appearing**: Check console for errors, verify script URL
2. **Styling conflicts**: Use `transparent: true`, adjust `zIndex`
3. **Mobile issues**: Enable `responsive: true`, test on devices

### Debug Mode

```javascript
LongvanChat.init({
  debug: true,
  // ... other options
});
```

## 📞 Support

For technical support or questions about the iframe integration:

- Check the documentation in `docs/iframe-integration.md`
- Review example implementations in `examples/`
- Test with the provided demo pages

---

**Ready to integrate?** Start with the basic integration example and customize from there! 🎉

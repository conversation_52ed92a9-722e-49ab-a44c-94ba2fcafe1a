/* Chat Widget Isolated Styles */
.chat-widget {
  /* CSS Variables for theming */
  --chat-primary: #3B82F6;
  --chat-secondary: #6B7280;
  --chat-success: #10B981;
  --chat-error: #EF4444;
  --chat-warning: #F59E0B;
  --chat-background: #F9FAFB;
  --chat-text: #1F2937;
  --chat-text-light: #6B7280;
  --chat-border: #E5E7EB;
  --chat-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Font family isolation */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  
  /* Reset styles to prevent inheritance */
  * {
    box-sizing: border-box;
  }
}

/* Custom animations for chat widget */
@keyframes chat-fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes chat-slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes chat-slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes chat-pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

@keyframes chat-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Chat widget specific classes */
.chat-widget .chat-animate-fade-in-up {
  animation: chat-fadeInUp 0.3s ease-out;
}

.chat-widget .chat-animate-slide-in-right {
  animation: chat-slideInRight 0.3s ease-out;
}

.chat-widget .chat-animate-slide-in-left {
  animation: chat-slideInLeft 0.3s ease-out;
}

.chat-widget .chat-animate-pulse-ring {
  animation: chat-pulse-ring 1.5s infinite;
}

.chat-widget .chat-animate-spin {
  animation: chat-spin 1s linear infinite;
}

/* Custom scrollbar for chat */
.chat-widget .chat-scrollbar {
  scroll-behavior: smooth;
}

.chat-widget .chat-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.chat-widget .chat-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.chat-widget .chat-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.chat-widget .chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .chat-widget .chat-window {
    width: calc(100vw - 2rem) !important;
    max-width: none !important;
    left: 1rem !important;
    right: 1rem !important;
  }
  
  .chat-widget .chat-window.chat-fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    border-radius: 0 !important;
    bottom: 0 !important;
    right: 0 !important;
  }
}

@media (max-width: 480px) {
  .chat-widget .chat-toggle-button {
    width: 3.5rem !important;
    height: 3.5rem !important;
  }
  
  .chat-widget .chat-window {
    height: calc(100vh - 6rem) !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .chat-widget {
    --chat-primary: #0066CC;
    --chat-secondary: #333333;
    --chat-border: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .chat-widget * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .chat-widget {
    --chat-background: #1F2937;
    --chat-text: #F9FAFB;
    --chat-text-light: #D1D5DB;
    --chat-border: #374151;
  }
}

/* Focus styles for accessibility */
.chat-widget button:focus,
.chat-widget input:focus {
  outline: 2px solid var(--chat-primary);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .chat-widget {
    display: none !important;
  }
}

{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "dist", "declaration": true, "declarationDir": "dist/types", "declarationMap": true, "emitDeclarationOnly": false, "noEmit": false, "isolatedModules": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "strict": true, "target": "ES2018", "lib": ["ES2018", "DOM", "DOM.Iterable"], "module": "ESNext", "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["src/**/*.test.ts", "src/**/*.test.tsx", "src/**/*.stories.ts", "src/**/*.stories.tsx", "dist", "node_modules", "**/*.spec.ts", "**/*.spec.tsx"]}
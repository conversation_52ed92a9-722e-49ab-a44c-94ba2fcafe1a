import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import tailwindcss from "@tailwindcss/vite";

// Danh sách các host đ<PERSON><PERSON><PERSON> phép
const getAllowedHosts = () => {
  const defaultHosts = ["localhost", "127.0.0.1", "0.0.0.0"];

  // Thêm các host từ environment variables
  const customHosts = process.env.VITE_ALLOWED_HOSTS?.split(",") || [];

  // Thêm các host cố định cho môi trường longvan.vn
  const longvanHosts = [
    "livechat.dev.longvan.vn",
    "livechat.longvan.vn",
    ".longvan.vn", // Cho phép tất cả subdomain của longvan.vn
  ];

  return [...defaultHosts, ...customHosts, ...longvanHosts];
};

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  server: {
    port: 3000,
    host: "0.0.0.0", // Cho phép kết nối từ bên ngoài container
    allowedHosts: getAllowedHosts(),
  },
  preview: {
    port: 3000,
    host: "0.0.0.0", // Cho phép kết nối từ bên ngoài container
    allowedHosts: getAllowedHosts(),
  },
});

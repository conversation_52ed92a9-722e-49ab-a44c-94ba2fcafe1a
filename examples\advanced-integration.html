<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Advanced Chat Widget Integration</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #f8fafc;
        color: #334155;
        line-height: 1.6;
      }

      .header {
        background: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 1rem 0;
        position: sticky;
        top: 0;
        z-index: 100;
      }

      .nav {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 2rem;
      }

      .logo {
        font-size: 1.5rem;
        font-weight: 700;
        color: #3b82f6;
      }

      .nav-links {
        display: flex;
        gap: 2rem;
        list-style: none;
      }

      .nav-links a {
        text-decoration: none;
        color: #64748b;
        font-weight: 500;
        transition: color 0.2s;
      }

      .nav-links a:hover {
        color: #3b82f6;
      }

      .main {
        max-width: 1200px;
        margin: 0 auto;
        padding: 4rem 2rem;
      }

      .hero {
        text-align: center;
        margin-bottom: 4rem;
      }

      .hero h1 {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .hero p {
        font-size: 1.25rem;
        color: #64748b;
        max-width: 600px;
        margin: 0 auto;
      }

      .controls {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 3rem;
      }

      .controls h2 {
        margin-bottom: 1.5rem;
        color: #1e293b;
      }

      .control-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }

      .control-group {
        display: flex;
        flex-direction: column;
      }

      .control-group label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #374151;
      }

      .control-group input,
      .control-group select {
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.2s;
      }

      .control-group input:focus,
      .control-group select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .button-group {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
      }

      .btn-primary {
        background: #3b82f6;
        color: white;
      }

      .btn-primary:hover {
        background: #2563eb;
        transform: translateY(-1px);
      }

      .btn-secondary {
        background: #f1f5f9;
        color: #475569;
      }

      .btn-secondary:hover {
        background: #e2e8f0;
      }

      .btn-danger {
        background: #ef4444;
        color: white;
      }

      .btn-danger:hover {
        background: #dc2626;
      }

      .status {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 2rem;
      }

      .status h3 {
        margin-bottom: 1rem;
        color: #1e293b;
      }

      .status-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f1f5f9;
      }

      .status-item:last-child {
        border-bottom: none;
      }

      .event-log {
        background: #1e293b;
        color: #e2e8f0;
        padding: 1.5rem;
        border-radius: 12px;
        font-family: "Courier New", monospace;
        font-size: 0.875rem;
        max-height: 300px;
        overflow-y: auto;
        margin-top: 1rem;
      }

      .event-log .event {
        margin-bottom: 0.5rem;
        padding: 0.25rem 0;
      }

      .event-log .timestamp {
        color: #94a3b8;
      }

      .event-log .event-type {
        color: #60a5fa;
        font-weight: 600;
      }

      @media (max-width: 768px) {
        .nav {
          padding: 0 1rem;
        }

        .nav-links {
          display: none;
        }

        .main {
          padding: 2rem 1rem;
        }

        .hero h1 {
          font-size: 2rem;
        }

        .control-grid {
          grid-template-columns: 1fr;
        }

        .button-group {
          flex-direction: column;
        }
      }
    </style>
  </head>
  <body>
    <header class="header">
      <nav class="nav">
        <div class="logo">ChatWidget Demo</div>
        <ul class="nav-links">
          <li><a href="#features">Features</a></li>
          <li><a href="#docs">Documentation</a></li>
          <li><a href="#support">Support</a></li>
        </ul>
      </nav>
    </header>

    <main class="main">
      <div class="hero">
        <h1>Advanced Integration Demo</h1>
        <p>
          Explore all the customization options and features of our chat widget
          with this interactive demo.
        </p>
      </div>

      <div class="controls">
        <h2>🎨 Customize Your Chat Widget</h2>
        <div class="control-grid">
          <div class="control-group">
            <label for="primaryColor">Primary Color</label>
            <input type="color" id="primaryColor" value="#3b82f6" />
          </div>

          <div class="control-group">
            <label for="companyName">Company Name</label>
            <input
              type="text"
              id="companyName"
              value="Your Company"
              placeholder="Enter company name"
            />
          </div>

          <div class="control-group">
            <label for="welcomeMessage">Welcome Message</label>
            <input
              type="text"
              id="welcomeMessage"
              value="Hello! How can we help?"
              placeholder="Enter welcome message"
            />
          </div>

          <div class="control-group">
            <label for="position">Position</label>
            <select id="position">
              <option value="bottom-right">Bottom Right</option>
              <option value="bottom-left">Bottom Left</option>
              <option value="top-right">Top Right</option>
              <option value="top-left">Top Left</option>
            </select>
          </div>

          <div class="control-group">
            <label for="width">Width</label>
            <input
              type="text"
              id="width"
              value="400px"
              placeholder="e.g., 400px, 50%"
            />
          </div>

          <div class="control-group">
            <label for="height">Height</label>
            <input
              type="text"
              id="height"
              value="600px"
              placeholder="e.g., 600px, 80vh"
            />
          </div>
        </div>

        <div class="button-group">
          <button class="btn btn-primary" onclick="updateWidget()">
            Apply Changes
          </button>
          <button class="btn btn-secondary" onclick="showWidget()">
            Show Widget
          </button>
          <button class="btn btn-secondary" onclick="hideWidget()">
            Hide Widget
          </button>
          <button class="btn btn-danger" onclick="destroyWidget()">
            Destroy Widget
          </button>
        </div>
      </div>

      <div class="status">
        <h3>📊 Widget Status</h3>
        <div class="status-item">
          <span>Status:</span>
          <span id="widgetStatus">Loading...</span>
        </div>
        <div class="status-item">
          <span>Visible:</span>
          <span id="widgetVisible">Unknown</span>
        </div>
        <div class="status-item">
          <span>Last Updated:</span>
          <span id="lastUpdated">Never</span>
        </div>
      </div>

      <div class="status">
        <h3>📝 Event Log</h3>
        <div class="event-log" id="eventLog">
          <div class="event">
            <span class="timestamp">[Loading...]</span>
            <span class="event-type">SYSTEM</span>
            <span>Initializing event log...</span>
          </div>
        </div>
      </div>
    </main>

    <!-- Load the embed script -->
    <script src="http://localhost:3000/embed.js"></script>

    <script>
      let widgetInitialized = false;

      // Initialize widget with default settings
      function initializeWidget() {
        if (widgetInitialized) {
          LongvanChat.destroy();
        }

        const config = getCurrentConfig();
        LongvanChat.init(config);
        widgetInitialized = true;
        updateStatus();
        logEvent("WIDGET_INITIALIZED", config);
      }

      // Get current configuration from form
      function getCurrentConfig() {
        return {
          primaryColor: document.getElementById("primaryColor").value,
          companyName: document.getElementById("companyName").value,
          welcomeMessage: document.getElementById("welcomeMessage").value,
          position: document.getElementById("position").value,
          width: document.getElementById("width").value,
          height: document.getElementById("height").value,
          responsive: true,
        };
      }

      // Update widget with new configuration
      function updateWidget() {
        initializeWidget();
        document.getElementById("lastUpdated").textContent =
          new Date().toLocaleTimeString();
      }

      // Show widget
      function showWidget() {
        if (widgetInitialized) {
          LongvanChat.show();
          logEvent("WIDGET_SHOWN");
          updateStatus();
        }
      }

      // Hide widget
      function hideWidget() {
        if (widgetInitialized) {
          LongvanChat.hide();
          logEvent("WIDGET_HIDDEN");
          updateStatus();
        }
      }

      // Destroy widget
      function destroyWidget() {
        if (widgetInitialized) {
          LongvanChat.destroy();
          widgetInitialized = false;
          logEvent("WIDGET_DESTROYED");
          updateStatus();
        }
      }

      // Update status display
      function updateStatus() {
        const statusEl = document.getElementById("widgetStatus");
        const visibleEl = document.getElementById("widgetVisible");

        if (!widgetInitialized) {
          statusEl.textContent = "Not Initialized";
          visibleEl.textContent = "No";
          return;
        }

        statusEl.textContent = "Initialized";
        visibleEl.textContent = LongvanChat.isVisible
          ? LongvanChat.isVisible()
            ? "Yes"
            : "No"
          : "Unknown";
      }

      // Log events
      function logEvent(type, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        const logEl = document.getElementById("eventLog");

        const eventEl = document.createElement("div");
        eventEl.className = "event";
        eventEl.innerHTML = `
                <span class="timestamp">[${timestamp}]</span>
                <span class="event-type">${type}</span>
                <span>${data ? JSON.stringify(data, null, 2) : ""}</span>
            `;

        logEl.appendChild(eventEl);
        logEl.scrollTop = logEl.scrollHeight;
      }

      // Listen for chat widget events
      window.addEventListener("message", function (event) {
        if (event.data.source === "longvan-chat") {
          logEvent(event.data.type, event.data.data);
          updateStatus();
        }
      });

      // Initialize on page load
      document.addEventListener("DOMContentLoaded", function () {
        setTimeout(initializeWidget, 1000);
      });
    </script>
  </body>
</html>

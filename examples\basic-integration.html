<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Basic Chat Widget Integration</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        margin: 0;
        padding: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: white;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
      }

      .hero {
        text-align: center;
        margin-bottom: 60px;
      }

      .hero h1 {
        font-size: 3rem;
        margin-bottom: 20px;
        font-weight: 700;
      }

      .hero p {
        font-size: 1.2rem;
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
      }

      .content {
        background: rgba(255, 255, 255, 0.1);
        padding: 40px;
        border-radius: 20px;
        backdrop-filter: blur(10px);
        margin-bottom: 40px;
      }

      .feature {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        padding: 20px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
      }

      .feature-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        font-size: 24px;
      }

      .feature-content h3 {
        margin: 0 0 10px 0;
        font-size: 1.3rem;
      }

      .feature-content p {
        margin: 0;
        opacity: 0.8;
      }

      .cta {
        text-align: center;
        padding: 40px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(10px);
      }

      .cta h2 {
        margin-bottom: 20px;
        font-size: 2rem;
      }

      .cta p {
        margin-bottom: 30px;
        font-size: 1.1rem;
        opacity: 0.9;
      }

      .btn {
        display: inline-block;
        padding: 15px 30px;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 2px solid rgba(255, 255, 255, 0.3);
      }

      .btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      .code-block {
        background: rgba(0, 0, 0, 0.3);
        padding: 20px;
        border-radius: 10px;
        font-family: "Courier New", monospace;
        font-size: 14px;
        overflow-x: auto;
        margin: 20px 0;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      @media (max-width: 768px) {
        body {
          padding: 20px;
        }

        .hero h1 {
          font-size: 2rem;
        }

        .content {
          padding: 20px;
        }

        .feature {
          flex-direction: column;
          text-align: center;
        }

        .feature-icon {
          margin-right: 0;
          margin-bottom: 15px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="hero">
        <h1>🚀 Welcome to Our Platform</h1>
        <p>
          Experience seamless customer support with our integrated chat widget.
          Get instant help whenever you need it.
        </p>
      </div>

      <div class="content">
        <h2>Why Choose Our Platform?</h2>

        <div class="feature">
          <div class="feature-icon">⚡</div>
          <div class="feature-content">
            <h3>Lightning Fast</h3>
            <p>
              Get instant responses from our support team with real-time
              messaging capabilities.
            </p>
          </div>
        </div>

        <div class="feature">
          <div class="feature-icon">🔒</div>
          <div class="feature-content">
            <h3>Secure & Private</h3>
            <p>
              Your conversations are encrypted and secure. We respect your
              privacy.
            </p>
          </div>
        </div>

        <div class="feature">
          <div class="feature-icon">📱</div>
          <div class="feature-content">
            <h3>Mobile Friendly</h3>
            <p>Works perfectly on all devices - desktop, tablet, and mobile.</p>
          </div>
        </div>

        <div class="feature">
          <div class="feature-icon">🎨</div>
          <div class="feature-content">
            <h3>Customizable</h3>
            <p>Fully customizable to match your brand colors and style.</p>
          </div>
        </div>
      </div>

      <div class="cta">
        <h2>Need Help?</h2>
        <p>
          Our support team is ready to assist you. Click the chat button in the
          bottom-right corner to get started!
        </p>
        <a href="#" class="btn" onclick="LongvanChat.show(); return false;"
          >Start Chat Now</a
        >
      </div>

      <div class="content">
        <h2>Integration Code</h2>
        <p>
          This page demonstrates the basic integration. Here's the code used:
        </p>

        <div class="code-block">
          &lt;!-- Load the embed script --&gt; &lt;script
          src="http://localhost:3000/embed.js"&gt;&lt;/script&gt; &lt;!--
          Initialize with basic configuration --&gt; &lt;script&gt;
          LongvanChat.init({ primaryColor: '#667eea', companyName: 'Demo
          Company', welcomeMessage: 'Hello! How can we help you today?',
          position: 'bottom-right' }); &lt;/script&gt;
        </div>
      </div>
    </div>

    <!-- Load the embed script -->
    <script src="http://localhost:3000/embed.js"></script>

    <!-- Initialize the chat widget -->
    <script>
      LongvanChat.init({
        primaryColor: "#667eea",
        companyName: "Demo Company",
        welcomeMessage: "Hello! How can we help you today?",
        position: "bottom-right",
        responsive: true,
      });

      // Log events for demonstration
      window.addEventListener("message", function (event) {
        if (event.data.source === "longvan-chat") {
          console.log("Chat Event:", event.data.type, event.data.data);
        }
      });
    </script>
  </body>
</html>

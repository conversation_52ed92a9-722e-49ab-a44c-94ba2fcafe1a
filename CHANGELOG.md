# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial release of Messenger Chat Widget
- React 16.8+ support with hooks
- TypeScript support with full type definitions
- Matrix protocol integration for real-time messaging
- Responsive design with mobile support
- Accessibility features (WCAG 2.1 compliant)
- Auto-reconnect functionality with localStorage persistence
- Customizable theming system
- Multiple positioning options
- UMD, CommonJS, and ES Module builds

### Features
- 🎨 Messenger-style UI with beautiful animations
- 🔄 Auto-reconnect with session persistence
- 📱 Responsive design for all devices
- ♿ Full accessibility support
- 🎯 TypeScript support
- 🎨 Customizable themes and branding
- 🔌 Matrix protocol integration
- 🚀 Optimized performance

### Technical
- Built with Rollup for optimal bundle size
- Comprehensive test suite with Jest and React Testing Library
- CI/CD pipeline with GitHub Actions
- Semantic versioning with automated releases
- Bundle size monitoring
- Cross-browser compatibility testing

## [1.0.0] - 2024-01-XX

### Added
- Initial stable release
- Core chat functionality
- Matrix integration
- Responsive design
- Accessibility features
- TypeScript support
- Comprehensive documentation

---

## Commit Convention

This project follows [Conventional Commits](https://www.conventionalcommits.org/):

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, etc.)
- `refactor:` - Code refactoring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks
- `perf:` - Performance improvements
- `ci:` - CI/CD changes

### Breaking Changes

Breaking changes should be indicated by:
- `BREAKING CHANGE:` in the commit footer
- `!` after the type/scope (e.g., `feat!:` or `feat(api)!:`)

### Examples

```
feat: add auto-reconnect functionality
fix: resolve CSS conflicts with host applications
docs: update installation guide
feat!: change API interface for better TypeScript support

BREAKING CHANGE: The ChatWidget config prop structure has changed.
See migration guide for details.
```

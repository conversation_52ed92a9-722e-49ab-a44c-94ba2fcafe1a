import React from "react";
import ReactDOM from "react-dom/client";
import { StandaloneChatWidget } from "./StandaloneChatWidget";
import type { ChatWidgetConfig } from "./useStandaloneMatrixChat";
import "./widget.css";

// Default configuration
const defaultConfig: ChatWidgetConfig = {
  primaryColor: "#3B82F6",
  secondaryColor: "#6B7280",
  companyName: "Customer Support",
  welcomeMessage: "Hello! How can we help you today?",
  position: "bottom-right",
  matrixServer: "https://matrix-synapse.longvan.vn",
  username: "demo",
  password: "demo",
  roomId: "!MnJuNdvpjxlMZkfjcn:matrix-synapse.longvan.vn",
  threadId: "$BJHuwFmtvl2LLO1Fs_JfBGyEzhU6eDgwFWiAmIeTJrk",
  zIndex: 9999,
  width: "400px",
  height: "600px",
  autoOpen: false,
  showBranding: true,
};

// Main widget class
class SimpleChatWidget {
  private config: ChatWidgetConfig;
  private container: HTMLElement | null = null;
  private root: ReactDOM.Root | null = null;
  private isInitialized = false;

  constructor(config: Partial<ChatWidgetConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  // Initialize the widget
  init(targetElement?: HTMLElement | string): void {
    if (this.isInitialized) {
      console.warn("SimpleChatWidget is already initialized");
      return;
    }

    // Find or create container
    if (targetElement) {
      if (typeof targetElement === "string") {
        this.container = document.querySelector(targetElement);
      } else {
        this.container = targetElement;
      }
    } else {
      this.container = this.createContainer();
      document.body.appendChild(this.container);
    }

    if (!this.container) {
      throw new Error("Could not find or create container element");
    }

    // Create React root and render
    this.root = ReactDOM.createRoot(this.container);
    this.render();
    this.isInitialized = true;

    // Auto-open if configured
    if (this.config.autoOpen) {
      setTimeout(() => this.open(), 1000);
    }
  }

  // Create default container
  private createContainer(): HTMLElement {
    const container = document.createElement("div");
    container.id = "simple-chat-widget-container";
    container.style.cssText = this.getContainerStyles();
    return container;
  }

  // Get container styles based on position
  private getContainerStyles(): string {
    const { position, zIndex } = this.config;
    let positionStyles = "";

    switch (position) {
      case "bottom-left":
        positionStyles = "bottom: 20px; left: 20px;";
        break;
      case "top-right":
        positionStyles = "top: 20px; right: 20px;";
        break;
      case "top-left":
        positionStyles = "top: 20px; left: 20px;";
        break;
      default:
        positionStyles = "bottom: 20px; right: 20px;";
    }

    return `
      position: fixed;
      ${positionStyles}
      z-index: ${zIndex};
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
  }

  // Render the React component
  private render(): void {
    if (!this.root) return;

    this.root.render(
      React.createElement(StandaloneChatWidget, {
        config: this.config,
        onToggle: this.handleToggle.bind(this),
        onMessage: this.handleMessage.bind(this),
      })
    );
  }

  // Event handlers
  private handleToggle(isOpen: boolean): void {
    this.dispatchEvent("toggle", { isOpen });
  }

  private handleMessage(message: any): void {
    this.dispatchEvent("message", { message });
  }

  // Dispatch custom events
  private dispatchEvent(type: string, detail: any): void {
    const event = new CustomEvent(`simpleChatWidget:${type}`, { detail });
    window.dispatchEvent(event);
  }

  // Public API methods
  open(): void {
    this.dispatchEvent("open", {});
  }

  close(): void {
    this.dispatchEvent("close", {});
  }

  toggle(): void {
    this.dispatchEvent("toggle", {});
  }

  updateConfig(newConfig: Partial<ChatWidgetConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.render();
  }

  destroy(): void {
    if (this.root) {
      this.root.unmount();
      this.root = null;
    }
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    this.container = null;
    this.isInitialized = false;
  }
}

// Global API
declare global {
  interface Window {
    SimpleChatWidget: typeof SimpleChatWidget;
    simpleChatWidget?: SimpleChatWidget;
  }
}

// Export for module usage
export { SimpleChatWidget, StandaloneChatWidget };
export type { ChatWidgetConfig };

// Global initialization for script tag usage
if (typeof window !== "undefined") {
  window.SimpleChatWidget = SimpleChatWidget;

  // Auto-initialize if script has data attributes
  document.addEventListener("DOMContentLoaded", () => {
    const script = document.querySelector('script[src*="simple-chat-widget"]');
    if (script?.hasAttribute("data-auto-init")) {
      const config: Partial<ChatWidgetConfig> = {};

      // Parse data attributes
      const attrs = [
        "primary-color",
        "secondary-color",
        "company-name",
        "welcome-message",
        "position",
        "matrix-server",
        "username",
        "password",
        "room-id",
        "thread-id",
        "auto-open",
        "show-branding",
      ];

      attrs.forEach((attr) => {
        const value = script.getAttribute(`data-${attr}`);
        if (value !== null) {
          const key = attr.replace(/-([a-z])/g, (_, c) =>
            c.toUpperCase()
          ) as keyof ChatWidgetConfig;
          if (attr === "auto-open" || attr === "show-branding") {
            (config as any)[key] = value === "true";
          } else {
            (config as any)[key] = value;
          }
        }
      });

      window.simpleChatWidget = new SimpleChatWidget(config);
      window.simpleChatWidget.init();
    }
  });
}

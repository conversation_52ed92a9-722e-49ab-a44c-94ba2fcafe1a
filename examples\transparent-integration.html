<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transparent Chat Widget Integration</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            position: relative;
        }
        
        /* Animated background pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
            z-index: 1;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }
        
        .hero {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 40px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            margin-bottom: 20px;
            font-size: 2rem;
            text-align: center;
        }
        
        .demo-section p {
            text-align: center;
            opacity: 0.9;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        
        .button-group {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .demo-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
        }
        
        .demo-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .demo-button.active {
            background: rgba(255, 255, 255, 0.4);
            border-color: rgba(255, 255, 255, 0.6);
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 40px;
        }
        
        .comparison-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .comparison-item h3 {
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .comparison-item p {
            opacity: 0.8;
            font-size: 0.95rem;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-top: 10px;
        }
        
        .status-transparent {
            background: rgba(0, 255, 0, 0.2);
            color: #90EE90;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }
        
        .status-solid {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        @media (max-width: 768px) {
            body {
                padding: 20px;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .demo-section {
                padding: 20px;
            }
            
            .comparison {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .button-group {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1>🌟 Transparent Chat Widget</h1>
            <p>Experience seamless integration with transparent background that blends perfectly with your website's design.</p>
        </div>
        
        <div class="demo-section">
            <h2>🎨 Background Mode Comparison</h2>
            <p>Switch between transparent and solid background modes to see the difference in integration.</p>
            
            <div class="button-group">
                <button class="demo-button" id="transparentBtn" onclick="setTransparentMode()">
                    🌟 Transparent Mode
                </button>
                <button class="demo-button active" id="solidBtn" onclick="setSolidMode()">
                    🎯 Solid Background
                </button>
                <button class="demo-button" onclick="toggleWidget()">
                    👁️ Toggle Widget
                </button>
            </div>
            
            <div class="comparison">
                <div class="comparison-item">
                    <h3>🌟 Transparent Mode</h3>
                    <p>Widget blends seamlessly with your page background. Perfect for artistic or colorful websites.</p>
                    <div class="status-indicator status-transparent" id="transparentStatus">
                        Inactive
                    </div>
                </div>
                
                <div class="comparison-item">
                    <h3>🎯 Solid Background</h3>
                    <p>Widget has its own background for better readability and professional appearance.</p>
                    <div class="status-indicator status-solid" id="solidStatus">
                        Active
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>💡 Integration Tips</h2>
            <p>Choose the right background mode based on your website's design:</p>
            
            <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                <h3>🌟 Use Transparent Mode When:</h3>
                <ul style="opacity: 0.9; line-height: 1.6;">
                    <li>Your website has colorful or artistic backgrounds</li>
                    <li>You want seamless visual integration</li>
                    <li>Your design is modern and minimalist</li>
                    <li>You want the widget to feel part of the page</li>
                </ul>
                
                <h3>🎯 Use Solid Background When:</h3>
                <ul style="opacity: 0.9; line-height: 1.6;">
                    <li>You prioritize readability and accessibility</li>
                    <li>Your website has busy or complex backgrounds</li>
                    <li>You want a professional, business-like appearance</li>
                    <li>You need consistent branding across all pages</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Load the embed script -->
    <script src="http://localhost:3000/embed.js"></script>
    
    <script>
        let currentMode = 'solid';
        let widgetVisible = false;
        
        // Initialize with solid background (default)
        function initializeWidget(transparent = false) {
            LongvanChat.destroy();
            LongvanChat.init({
                primaryColor: '#667eea',
                companyName: 'Transparent Demo',
                welcomeMessage: transparent ? 
                    'Hello! I have a transparent background that blends with your page.' :
                    'Hello! I have a solid background for better readability.',
                position: 'bottom-right',
                transparent: transparent,
                responsive: true,
                width: '400px',
                height: '600px'
            });
            widgetVisible = true;
        }
        
        // Set transparent mode
        function setTransparentMode() {
            currentMode = 'transparent';
            initializeWidget(true);
            updateUI();
        }
        
        // Set solid mode
        function setSolidMode() {
            currentMode = 'solid';
            initializeWidget(false);
            updateUI();
        }
        
        // Toggle widget visibility
        function toggleWidget() {
            if (widgetVisible) {
                LongvanChat.hide();
                widgetVisible = false;
            } else {
                LongvanChat.show();
                widgetVisible = true;
            }
        }
        
        // Update UI indicators
        function updateUI() {
            const transparentBtn = document.getElementById('transparentBtn');
            const solidBtn = document.getElementById('solidBtn');
            const transparentStatus = document.getElementById('transparentStatus');
            const solidStatus = document.getElementById('solidStatus');
            
            // Update button states
            transparentBtn.classList.toggle('active', currentMode === 'transparent');
            solidBtn.classList.toggle('active', currentMode === 'solid');
            
            // Update status indicators
            transparentStatus.textContent = currentMode === 'transparent' ? 'Active' : 'Inactive';
            solidStatus.textContent = currentMode === 'solid' ? 'Active' : 'Inactive';
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                setSolidMode(); // Start with solid background
            }, 1000);
        });
        
        // Listen for chat widget events
        window.addEventListener('message', function(event) {
            if (event.data.source === 'longvan-chat') {
                console.log('Chat Event:', event.data.type, event.data.data);
            }
        });
    </script>
</body>
</html>

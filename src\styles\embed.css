/* Iframe-specific styles for embeddable chat widget */

/* Reset and base styles for iframe */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: transparent !important;
}

#root {
  width: 100%;
  height: 100%;
  background: transparent !important;
}

/* Transparent background support */
.embed-transparent {
  background: transparent !important;
}

.embed-transparent * {
  background-color: transparent !important;
}

/* Click-through functionality for non-interactive areas */
.embed-clickthrough {
  pointer-events: none;
}

.embed-clickthrough .interactive {
  pointer-events: auto;
}

/* Responsive design for iframe */
.embed-responsive {
  width: 100%;
  height: 100%;
  min-height: 400px;
  max-height: 100vh;
  overflow: hidden;
}

/* Remove default margins and padding */
.embed-container {
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Ensure proper sizing */
.embed-chat-window {
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  transform: none !important;
}

/* Remove fixed positioning for iframe context */
.embed-chat-window .fixed {
  position: relative !important;
}

/* Adjust toggle button for iframe */
.embed-toggle-button {
  position: absolute !important;
  top: 1rem !important;
  right: 1rem !important;
  z-index: 1000 !important;
}

/* Smooth animations for iframe */
.embed-animation {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Scrollbar styling for iframe */
.embed-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.embed-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.embed-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.embed-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.embed-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Focus styles for accessibility */
.embed-focus:focus {
  outline: 2px solid var(--primary-color, #3B82F6);
  outline-offset: 2px;
}

/* Loading states */
.embed-loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Error states */
.embed-error {
  border: 1px solid #ef4444;
  background-color: #fef2f2;
}

/* Success states */
.embed-success {
  border: 1px solid #10b981;
  background-color: #f0fdf4;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .embed-responsive {
    min-height: 300px;
  }
  
  .embed-chat-window {
    font-size: 14px;
  }
  
  .embed-toggle-button {
    width: 48px !important;
    height: 48px !important;
  }
}

@media (max-width: 480px) {
  .embed-responsive {
    min-height: 250px;
  }
  
  .embed-chat-window {
    font-size: 13px;
  }
  
  .embed-toggle-button {
    width: 44px !important;
    height: 44px !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .embed-chat-window {
    border: 2px solid currentColor;
  }
  
  .embed-toggle-button {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .embed-animation {
    transition: none;
  }
  
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .embed-chat-window {
    color-scheme: dark;
  }
}

/* Print styles */
@media print {
  .embed-chat-window {
    display: none !important;
  }
}

# 🔄 Migration Guide

This guide helps you migrate between different versions of the Messenger Chat Widget.

## Version 2.0.0 (Future)

### Breaking Changes

#### Config Structure Changes

**Before (v1.x):**
```tsx
<ChatWidget 
  matrixServer="https://server.com"
  apiEndpoint="https://api.com"
  primaryColor="#3B82F6"
  position="bottom-right"
/>
```

**After (v2.x):**
```tsx
<ChatWidget 
  config={{
    matrixServer: "https://server.com",
    apiEndpoint: "https://api.com",
    theme: {
      primaryColor: "#3B82F6",
      position: "bottom-right"
    }
  }}
/>
```

#### CSS Class Changes

**Before (v1.x):**
```css
.messenger-chat-widget { /* styles */ }
.messenger-scrollbar { /* styles */ }
```

**After (v2.x):**
```css
.chat-widget { /* styles */ }
.chat-scrollbar { /* styles */ }
```

### Migration Steps

1. **Update imports:**
```tsx
// Before
import ChatWidget from '@yourcompany/messenger-chat-widget';

// After
import { ChatWidget } from '@yourcompany/messenger-chat-widget';
```

2. **Update configuration:**
```tsx
// Before
const config = {
  matrixServer: "...",
  primaryColor: "...",
  position: "..."
};

// After
const config = {
  matrixServer: "...",
  theme: {
    primaryColor: "...",
    position: "..."
  }
};
```

3. **Update CSS classes:**
```css
/* Before */
.messenger-chat-widget .custom-style { }

/* After */
.chat-widget .custom-style { }
```

## Version 1.1.0

### New Features

#### Theme Provider Support

```tsx
// New in v1.1.0
import { ChatWidget, ChatThemeProvider } from '@yourcompany/messenger-chat-widget';

function App() {
  return (
    <ChatThemeProvider theme={{ primaryColor: '#FF6B6B' }}>
      <ChatWidget />
    </ChatThemeProvider>
  );
}
```

#### Multiple Widget Support

```tsx
// New in v1.1.0
<ChatWidget id="support" config={{ /* support config */ }} />
<ChatWidget id="sales" config={{ /* sales config */ }} />
```

### Migration Steps

1. **Optional: Use Theme Provider**
   - No breaking changes
   - Can gradually adopt new theme system

2. **Optional: Add widget IDs**
   - Recommended for multiple widgets
   - Backward compatible

## Version 1.0.0

### Initial Release

#### Features
- Basic chat functionality
- Matrix integration
- Responsive design
- TypeScript support

#### Installation
```bash
npm install @yourcompany/messenger-chat-widget@1.0.0
```

## Common Migration Issues

### CSS Conflicts

**Issue:** Widget styles conflict with host application

**Solution:**
```css
/* Isolate widget styles */
.chat-widget {
  all: initial;
  font-family: system-ui, sans-serif;
}

/* Or use CSS modules */
.widget :global(.chat-widget) {
  /* styles */
}
```

### TypeScript Errors

**Issue:** Type definitions not found

**Solution:**
```typescript
// Add to types/index.d.ts
declare module '@yourcompany/messenger-chat-widget' {
  export interface ChatWidgetConfig {
    // Add missing types
  }
}
```

### Bundle Size Issues

**Issue:** Widget increases bundle size significantly

**Solution:**
```tsx
// Lazy load the widget
const ChatWidget = React.lazy(() => 
  import('@yourcompany/messenger-chat-widget').then(mod => ({
    default: mod.ChatWidget
  }))
);

function App() {
  return (
    <Suspense fallback={<div>Loading chat...</div>}>
      <ChatWidget />
    </Suspense>
  );
}
```

### React Version Compatibility

**Issue:** Widget doesn't work with older React versions

**Solution:**
```bash
# Check React version
npm ls react

# Upgrade if needed
npm install react@^18.0.0 react-dom@^18.0.0

# Or use compatible version
npm install @yourcompany/messenger-chat-widget@^1.0.0
```

## Automated Migration Tools

### Codemod for v1 to v2

```bash
# Install codemod
npm install -g @yourcompany/chat-widget-codemod

# Run migration
npx @yourcompany/chat-widget-codemod v1-to-v2 src/
```

### ESLint Rules

```javascript
// .eslintrc.js
module.exports = {
  rules: {
    '@yourcompany/chat-widget/no-deprecated-props': 'error',
    '@yourcompany/chat-widget/prefer-config-object': 'warn'
  }
};
```

## Testing After Migration

### Unit Tests

```tsx
import { render } from '@testing-library/react';
import { ChatWidget } from '@yourcompany/messenger-chat-widget';

test('widget renders after migration', () => {
  const { container } = render(<ChatWidget />);
  expect(container.firstChild).toBeInTheDocument();
});
```

### Integration Tests

```tsx
test('widget works with new config structure', () => {
  const config = {
    theme: {
      primaryColor: '#FF6B6B'
    }
  };
  
  render(<ChatWidget config={config} />);
  // Test functionality
});
```

### Visual Regression Tests

```bash
# Run visual tests after migration
npm run test:visual

# Update snapshots if needed
npm run test:visual -- --updateSnapshot
```

## Rollback Plan

### If Migration Fails

1. **Revert package version:**
```bash
npm install @yourcompany/messenger-chat-widget@1.0.0
```

2. **Restore previous code:**
```bash
git checkout HEAD~1 -- src/components/ChatWidget
```

3. **Clear cache:**
```bash
npm run clean
rm -rf node_modules/.cache
```

## Support

If you encounter issues during migration:

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/yourcompany/messenger-chat-widget/issues)
- 💬 Discord: [Community Chat](https://discord.gg/yourcompany)
- 📖 Docs: [Migration Documentation](https://docs.yourcompany.com/migration)

## Version Support Policy

- **Current version (2.x)**: Full support, new features
- **Previous version (1.x)**: Security fixes only
- **Legacy versions (0.x)**: No support

We recommend staying on the latest stable version for the best experience and security.

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Embed Script Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
        }
        
        .demo-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .demo-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 16px;
        }
        
        .demo-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.15);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #fff;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(16, 185, 129, 0.3);
            border: 1px solid #10b981;
        }
        
        .status.error {
            background: rgba(239, 68, 68, 0.3);
            border: 1px solid #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Embed Script Test</h1>
        
        <div class="content">
            <h2>Embed Script Status</h2>
            <div id="embedStatus" class="status">Loading...</div>
            
            <div class="info-box">
                <h3>📋 Current Test Configuration:</h3>
                <ul>
                    <li><strong>Script URL:</strong> <code>http://localhost:3000/embed.js</code></li>
                    <li><strong>Target URL:</strong> <code>http://localhost:3000/</code> (root page)</li>
                    <li><strong>Method:</strong> JavaScript embed script</li>
                </ul>
            </div>
        </div>
        
        <div class="content">
            <h2>🎮 Test Different Configurations</h2>
            <div class="demo-buttons">
                <button class="demo-button" onclick="testDefault()">
                    Test Default
                </button>
                <button class="demo-button" onclick="testCustomRed()">
                    Test Red Theme
                </button>
                <button class="demo-button" onclick="testCustomGreen()">
                    Test Green Theme
                </button>
                <button class="demo-button" onclick="testCustomPurple()">
                    Test Purple Theme
                </button>
                <button class="demo-button" onclick="destroyWidget()">
                    Destroy Widget
                </button>
                <button class="demo-button" onclick="showWidget()">
                    Show Widget
                </button>
                <button class="demo-button" onclick="hideWidget()">
                    Hide Widget
                </button>
            </div>
        </div>
        
        <div class="content">
            <h2>📋 Integration Code Examples</h2>
            
            <h3>Basic Integration:</h3>
            <div class="code-block">
&lt;script src="http://localhost:3000/embed.js"&gt;&lt;/script&gt;
&lt;script&gt;
  LongvanChat.init();
&lt;/script&gt;
            </div>
            
            <h3>With Custom Configuration:</h3>
            <div class="code-block">
&lt;script src="http://localhost:3000/embed.js"&gt;&lt;/script&gt;
&lt;script&gt;
  LongvanChat.init({
    primaryColor: '#FF6B6B',
    companyName: 'Your Company',
    welcomeMessage: 'Hello! How can we help?',
    position: 'bottom-right'
  });
&lt;/script&gt;
            </div>
            
            <h3>Auto-initialization with Data Attributes:</h3>
            <div class="code-block">
&lt;script 
  src="http://localhost:3000/embed.js"
  data-auto-init="true"
  data-primary-color="#FF6B6B"
  data-company-name="Your Company"
  data-position="bottom-right"&gt;
&lt;/script&gt;
            </div>
        </div>
        
        <div class="content">
            <h2>🔧 Event Monitoring</h2>
            <div id="eventLog" style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
                <div>Event log will appear here...</div>
            </div>
        </div>
    </div>

    <!-- Load the embed script -->
    <script src="http://localhost:3000/embed.js"></script>
    
    <script>
        let eventLogContainer = document.getElementById('eventLog');
        let statusContainer = document.getElementById('embedStatus');
        
        function logEvent(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            eventLogContainer.appendChild(logEntry);
            eventLogContainer.scrollTop = eventLogContainer.scrollHeight;
        }
        
        function updateStatus(message, isSuccess = true) {
            statusContainer.textContent = message;
            statusContainer.className = `status ${isSuccess ? 'success' : 'error'}`;
        }
        
        // Check if LongvanChat is loaded
        if (window.LongvanChat) {
            updateStatus('✅ Embed script loaded successfully!', true);
            logEvent('LongvanChat object is available');
        } else {
            updateStatus('❌ Failed to load embed script', false);
            logEvent('LongvanChat object not found');
        }
        
        // Listen for chat widget events
        window.addEventListener('message', function(event) {
            if (event.data.source === 'longvan-chat') {
                logEvent(`Received: ${event.data.type} - ${JSON.stringify(event.data.data || {})}`);
            }
        });
        
        // Test functions
        function testDefault() {
            if (window.LongvanChat) {
                window.LongvanChat.destroy();
                window.LongvanChat.init();
                logEvent('Initialized default chat widget');
            }
        }
        
        function testCustomRed() {
            if (window.LongvanChat) {
                window.LongvanChat.destroy();
                window.LongvanChat.init({
                    primaryColor: '#FF6B6B',
                    companyName: 'Red Support Team',
                    welcomeMessage: 'Welcome to our red-themed support!',
                    position: 'bottom-right'
                });
                logEvent('Initialized red-themed chat widget');
            }
        }
        
        function testCustomGreen() {
            if (window.LongvanChat) {
                window.LongvanChat.destroy();
                window.LongvanChat.init({
                    primaryColor: '#10B981',
                    companyName: 'Green Support Team',
                    welcomeMessage: 'Eco-friendly support team here!',
                    position: 'bottom-left'
                });
                logEvent('Initialized green-themed chat widget');
            }
        }
        
        function testCustomPurple() {
            if (window.LongvanChat) {
                window.LongvanChat.destroy();
                window.LongvanChat.init({
                    primaryColor: '#8B5CF6',
                    companyName: 'Purple Premium Support',
                    welcomeMessage: 'Premium support experience awaits!',
                    position: 'top-right'
                });
                logEvent('Initialized purple-themed chat widget');
            }
        }
        
        function destroyWidget() {
            if (window.LongvanChat) {
                window.LongvanChat.destroy();
                logEvent('Chat widget destroyed');
            }
        }
        
        function showWidget() {
            if (window.LongvanChat) {
                window.LongvanChat.show();
                logEvent('Chat widget shown');
            }
        }
        
        function hideWidget() {
            if (window.LongvanChat) {
                window.LongvanChat.hide();
                logEvent('Chat widget hidden');
            }
        }
        
        // Auto-initialize with default settings
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (window.LongvanChat) {
                    testDefault();
                }
            }, 1000);
        });
    </script>
</body>
</html>

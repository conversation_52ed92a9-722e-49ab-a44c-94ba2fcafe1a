@import "tailwindcss";
@import "./styles/embed.css";

/* Custom animations for Messenger-style chat */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

@keyframes typing-dots {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* Utility classes */
.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.animate-pulse-ring {
  animation: pulse-ring 1.5s infinite;
}

.animate-typing-dots {
  animation: typing-dots 1.4s infinite;
}

/* Messenger-style scrollbar */
.messenger-scrollbar {
  scroll-behavior: smooth;
}

.messenger-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.messenger-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.messenger-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.messenger-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Chat bubble animations */
.chat-bubble-enter {
  animation: fadeInUp 0.3s ease-out;
}

.chat-bubble-user {
  animation: slideInRight 0.4s ease-out;
  transform-origin: right center;
}

.chat-bubble-other {
  animation: slideInLeft 0.4s ease-out;
  transform-origin: left center;
}

/* Loading spinner improvements */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button hover effects */
.btn-messenger {
  transition: all 0.2s ease-in-out;
}

.btn-messenger:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-messenger:active {
  transform: translateY(0);
}

/* Chat window entrance animation */
.chat-window-enter {
  animation: fadeInUp 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Smooth transitions for all elements */
* {
  transition: all 0.2s ease-in-out;
}

/* Message status indicators */
.message-sending {
  opacity: 0.7;
}

.message-sent {
  opacity: 1;
}

.message-error {
  opacity: 0.5;
  border: 1px solid #ef4444;
}

/* Iframe embedding styles */
.iframe-mode {
  background: transparent !important;
  margin: 0 !important;
  padding: 0 !important;
  height: 100vh !important;
  width: 100vw !important;
  overflow: hidden !important;
}

.iframe-mode body {
  background: transparent !important;
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  overflow: hidden !important;
}

.iframe-mode #root {
  background: transparent !important;
  height: 100vh !important;
  width: 100vw !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Embed transparent mode */
.embed-transparent {
  background: transparent !important;
}

.embed-transparent body {
  background: transparent !important;
}

.embed-transparent #root {
  background: transparent !important;
}

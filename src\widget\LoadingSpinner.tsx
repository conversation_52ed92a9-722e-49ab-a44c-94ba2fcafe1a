import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'white' | 'gray';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'blue',
  className = '',
}) => {
  const sizeClasses = {
    sm: 'scw-spinner-sm',
    md: 'scw-spinner-md',
    lg: 'scw-spinner-lg',
  };

  const colorClasses = {
    blue: 'scw-spinner-blue',
    white: 'scw-spinner-white',
    gray: 'scw-spinner-gray',
  };

  return (
    <div className={`scw-spinner ${sizeClasses[size]} ${colorClasses[color]} ${className}`}>
      <svg
        className="scw-spinner-svg"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="scw-spinner-circle"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="scw-spinner-path"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
  );
};

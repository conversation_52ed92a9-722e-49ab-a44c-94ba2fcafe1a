stages:
  - build
  - docker

variables:
  DOCKER_REGISTRY: containers-registry.longvan.vn/longvan-docker
  FF_USE_POD_ACTIVE_DEADLINE_SECONDS: "false" # Tắt deadline để tránh timeout pod

build_app:
  stage: build
  image: node:20-alpine # Image nhẹ, đồng bộ với Dockerfile
  script:
    - npm config set legacy-peer-deps true # <PERSON><PERSON><PERSON> bảo tương thích phụ thuộc
    - npm install # Cài đặt phụ thuộc
    - npm run build:$DEPLOY_ENV # Build ứng dụng với môi trường tương ứng
  artifacts:
    paths:
      - dist/ # <PERSON><PERSON><PERSON> thư mục dist/ làm artifact
  rules:
    - if: '$CI_COMMIT_REF_NAME == "development"'
      variables:
        DEPLOY_ENV: development
    - if: '$CI_COMMIT_REF_NAME == "production"'
      variables:
        DEPLOY_ENV: production
    - if: '$CI_COMMIT_REF_NAME == "production"'
      variables:
        DEPLOY_ENV: pre-production
    - when: never # Không chạy nếu không phải nhánh development/production

build_docker:
  stage: docker
  image: gcr.io/kaniko-project/executor:v1.22.0-debug # Phiên bản ổn định
  script:
    - SHORT_SHA=$(echo "$CI_COMMIT_SHA" | cut -c1-7)
    - /kaniko/executor --context . --dockerfile Dockerfile --build-arg ENV=$DEPLOY_ENV --destination $DOCKER_REGISTRY/livechat-matrix-service:$SHORT_SHA-$DEPLOY_ENV --cache=true
  rules:
    - if: '$CI_COMMIT_REF_NAME == "development"'
      variables:
        DEPLOY_ENV: development
    - if: '$CI_COMMIT_REF_NAME == "production"'
      variables:
        DEPLOY_ENV: production
    - if: '$CI_COMMIT_REF_NAME == "pre-production"'
      variables:
        DEPLOY_ENV: production
    - when: never
  dependencies:
    - build_app

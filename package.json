{"name": "live-chat", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000 --host 0.0.0.0", "build": "tsc -b && vite build", "build:development": "vite build --mode development", "build:production": "vite build --mode production", "build:pre-production": "vite build --mode pre-production", "build:widget": "vite build --config vite.widget.config.ts", "start:development": "vite --mode development --port 3000 --host 0.0.0.0", "start:production": "vite preview --port 3000 --host 0.0.0.0", "start:pre-production": "vite preview --port 3000 --host 0.0.0.0", "lint": "eslint .", "preview": "vite preview --port 3000 --host 0.0.0.0"}, "dependencies": {"@tailwindcss/vite": "^4.1.5", "@types/react-router-dom": "^5.3.3", "axios": "^1.9.0", "matrix-js-sdk": "^37.5.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-to-webcomponent": "^2.0.1", "tailwindcss": "^4.1.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "terser": "^5.42.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}
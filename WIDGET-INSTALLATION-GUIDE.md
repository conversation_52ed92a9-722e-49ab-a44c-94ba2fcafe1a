# Simple Chat Widget - Installation & Usage Guide

## 🚀 Quick Start

Your `SimpleChatWindow` component has been packaged as a standalone, reusable widget that can be integrated into any website or application. Here's how to use it:

## 📦 Building the Widget

First, build the widget package:

```bash
# Build the standalone widget
npm run build:widget

# Or use the build script
./scripts/build-widget.sh
```

This creates a `dist/widget/` directory with all the necessary files.

## 🔧 Integration Methods

### Method 1: Script Tag (Easiest - No Framework Required)

Perfect for any website, regardless of the technology stack.

```html
<!DOCTYPE html>
<html>
<head>
    <title>My Website</title>
</head>
<body>
    <!-- Your website content -->
    <h1>Welcome to my website!</h1>
    
    <!-- Load the chat widget -->
    <script src="path/to/simple-chat-widget.umd.js"></script>
    <script>
        // Initialize the widget
        const widget = new SimpleChatWidget({
            primaryColor: '#FF6B6B',
            companyName: 'Your Company',
            welcomeMessage: 'Hello! How can we help you today?',
            position: 'bottom-right'
        });
        widget.init();
    </script>
</body>
</html>
```

### Method 2: Auto-initialization with Data Attributes

Even easier - just add the script with configuration in data attributes:

```html
<script 
    src="path/to/simple-chat-widget.umd.js"
    data-auto-init="true"
    data-primary-color="#FF6B6B"
    data-company-name="Your Company"
    data-welcome-message="Hello! How can we help?"
    data-position="bottom-right"
    data-auto-open="false"
    data-show-branding="true">
</script>
```

### Method 3: React Component (For React Applications)

If you're using React, you can use it as a proper React component:

```bash
# Install as NPM package (after publishing)
npm install @yourcompany/simple-chat-widget
```

```jsx
import React from 'react';
import { SimpleChatWidget } from '@yourcompany/simple-chat-widget';
import '@yourcompany/simple-chat-widget/dist/style.css';

function App() {
    return (
        <div>
            <h1>My React App</h1>
            
            <SimpleChatWidget
                primaryColor="#FF6B6B"
                companyName="Your Company"
                welcomeMessage="Hello! How can we help?"
                position="bottom-right"
                onToggle={(isOpen) => console.log('Chat toggled:', isOpen)}
                onMessage={(message) => console.log('New message:', message)}
                onChatStarted={(userInfo) => console.log('Chat started:', userInfo)}
            />
        </div>
    );
}

export default App;
```

### Method 4: Web Component (For Any Framework)

Works with Vue, Angular, Svelte, or any other framework:

```html
<!-- Load the widget -->
<script src="path/to/simple-chat-widget.umd.js"></script>

<!-- Use as a web component -->
<simple-chat-widget
    primary-color="#FF6B6B"
    company-name="Your Company"
    welcome-message="Hello! How can we help?"
    position="bottom-right">
</simple-chat-widget>
```

## ⚙️ Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `primaryColor` | string | `#3B82F6` | Primary color for buttons and user messages |
| `secondaryColor` | string | `#6B7280` | Secondary color for UI elements |
| `companyName` | string | `Customer Support` | Company name shown in header |
| `welcomeMessage` | string | `Hello! How can we help you today?` | Welcome message text |
| `position` | string | `bottom-right` | Widget position (`bottom-right`, `bottom-left`, `top-right`, `top-left`) |
| `width` | string | `400px` | Widget width |
| `height` | string | `600px` | Widget height |
| `zIndex` | number | `9999` | CSS z-index |
| `autoOpen` | boolean | `false` | Auto-open chat on load |
| `showBranding` | boolean | `true` | Show "Powered by" text |
| `matrixServer` | string | - | Your Matrix server URL |
| `username` | string | - | Matrix bot username |
| `password` | string | - | Matrix bot password |
| `roomId` | string | - | Matrix room ID |
| `threadId` | string | - | Matrix thread ID |

## 🎨 Customization

### Custom Colors and Branding

```javascript
const widget = new SimpleChatWidget({
    primaryColor: '#FF6B6B',        // Your brand color
    secondaryColor: '#6B7280',      // Secondary elements
    companyName: 'Acme Corp',       // Your company name
    welcomeMessage: 'Hi there! Our support team is ready to help you.',
    showBranding: false             // Hide "Powered by" text
});
```

### Custom Positioning and Size

```javascript
const widget = new SimpleChatWidget({
    position: 'bottom-left',        // Position on screen
    width: '350px',                 // Custom width
    height: '500px',                // Custom height
    zIndex: 10000                   // Ensure it's on top
});
```

### Matrix Server Configuration

```javascript
const widget = new SimpleChatWidget({
    matrixServer: 'https://your-matrix-server.com',
    username: 'support-bot',
    password: 'your-secure-password',
    roomId: '!your-room-id:your-server.com',
    threadId: '$your-thread-id'
});
```

## 📱 Responsive Design

The widget automatically adapts to different screen sizes:
- **Desktop**: Positioned as configured (bottom-right, etc.)
- **Mobile**: Full-screen overlay for better usability

## 🔧 Advanced Usage

### Event Handling

```javascript
const widget = new SimpleChatWidget({
    primaryColor: '#FF6B6B',
    companyName: 'Your Company'
});

// Listen for events
window.addEventListener('simpleChatWidget:toggle', (event) => {
    console.log('Chat toggled:', event.detail.isOpen);
    // Track analytics, update UI, etc.
});

window.addEventListener('simpleChatWidget:message', (event) => {
    console.log('New message:', event.detail.message);
    // Handle new messages
});

widget.init();
```

### Programmatic Control

```javascript
// Control the widget programmatically
widget.open();          // Open the chat
widget.close();         // Close the chat
widget.toggle();        // Toggle open/close
widget.destroy();       // Remove the widget completely

// Update configuration
widget.updateConfig({
    primaryColor: '#10B981',
    companyName: 'New Company Name'
});
```

## 🚀 Deployment

### Option 1: Self-Hosted

1. Build the widget: `npm run build:widget`
2. Upload `dist/widget/simple-chat-widget.umd.js` to your server
3. Include it in your HTML as shown above

### Option 2: CDN (Recommended)

Upload to a CDN like AWS CloudFront, Cloudflare, or similar:

```html
<script src="https://your-cdn.com/simple-chat-widget.umd.js"></script>
```

### Option 3: NPM Package

1. Update `chat-widget-package.json` with your details
2. Publish to NPM: `npm publish dist/widget/`
3. Install in projects: `npm install @yourcompany/simple-chat-widget`

## 🧪 Testing

Test your widget integration:

1. Open `dist/widget/test.html` in your browser
2. Try the examples in `dist/widget/examples/`
3. Test on different devices and screen sizes

## 🔒 Security Considerations

- Always use HTTPS in production
- Keep Matrix credentials secure
- Consider using Matrix application service tokens
- Implement proper CORS policies on your Matrix server

## 📊 Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🆘 Troubleshooting

### Widget Not Loading
- Check browser console for errors
- Verify script path is correct
- Ensure CORS headers are set if loading from different domain

### Matrix Connection Issues
- Verify Matrix server URL and credentials
- Check Matrix server CORS configuration
- Ensure room and thread IDs are correct

### Styling Issues
- Widget styles are scoped with `scw-` prefix
- Use `!important` if you need to override styles
- Check for CSS conflicts with your site's styles

## 📞 Support

For help and questions:
- Check the examples in `examples/` directory
- Review the build output in `dist/widget/BUILD_REPORT.txt`
- Open an issue on GitHub

Your standalone chat widget is now ready for production use! 🎉

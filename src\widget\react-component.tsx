import React from "react";
import SimpleChatWindow from "../components/chat/SimpleChatWindow";
import type { ChatWidgetConfig } from "./index";
import "../index.css"; // Import Tailwind CSS

// Export the main component for React applications
export interface SimpleChatWidgetProps extends Partial<ChatWidgetConfig> {
  className?: string;
  style?: React.CSSProperties;
}

export const SimpleChatWidget: React.FC<SimpleChatWidgetProps> = ({
  position = "bottom-right",
  zIndex = 9999,
  className,
  style,
}) => {
  const containerStyle: React.CSSProperties = {
    position: "fixed",
    zIndex,
    ...style,
  };

  // Apply position-specific styles
  switch (position) {
    case "bottom-left":
      containerStyle.bottom = "20px";
      containerStyle.left = "20px";
      break;
    case "top-right":
      containerStyle.top = "20px";
      containerStyle.right = "20px";
      break;
    case "top-left":
      containerStyle.top = "20px";
      containerStyle.left = "20px";
      break;
    default: // bottom-right
      containerStyle.bottom = "20px";
      containerStyle.right = "20px";
  }

  return (
    <div className={className} style={containerStyle}>
      <SimpleChatWindow />
    </div>
  );
};

// Export types
export type { ChatWidgetConfig };

// Export the original component as well
export { default as SimpleChatWindow } from "../components/chat/SimpleChatWindow";

// Default export
export default SimpleChatWidget;

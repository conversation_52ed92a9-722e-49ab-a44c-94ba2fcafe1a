import React from "react";
import { StandaloneChatWidget } from "./StandaloneChatWidget";
import type { ChatWidgetConfig } from "./useStandaloneMatrixChat";
import "./widget.css";

// Export the main component for React applications
export interface SimpleChatWidgetProps extends Partial<ChatWidgetConfig> {
  onToggle?: (isOpen: boolean) => void;
  onMessage?: (message: any) => void;
  onChatStarted?: (userInfo: any) => void;
  onChatClosed?: () => void;
}

export const SimpleChatWidget: React.FC<SimpleChatWidgetProps> = ({
  onToggle,
  onMessage,
  onChatStarted,
  onChatClosed,
  ...configProps
}) => {
  const config: ChatWidgetConfig = {
    primaryColor: "#3B82F6",
    secondaryColor: "#6B7280",
    companyName: "Customer Support",
    welcomeMessage: "Hello! How can we help you today?",
    position: "bottom-right",
    matrixServer: "https://matrix-synapse.longvan.vn",
    username: "demo",
    password: "demo",
    roomId: "!MnJuNdvpjxlMZkfjcn:matrix-synapse.longvan.vn",
    threadId: "$BJHuwFmtvl2LLO1Fs_JfBGyEzhU6eDgwFWiAmIeTJrk",
    zIndex: 9999,
    width: "400px",
    height: "600px",
    autoOpen: false,
    showBranding: true,
    ...configProps,
  };

  return (
    <StandaloneChatWidget
      config={config}
      onToggle={onToggle}
      onMessage={onMessage}
    />
  );
};

// Export types
export type { ChatWidgetConfig };

// Export the standalone component as well
export { StandaloneChatWidget };

// Default export
export default SimpleChatWidget;

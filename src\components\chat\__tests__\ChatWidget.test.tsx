import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ChatWidget from '../ChatWidget';

// Mock the useMatrixChat hook
jest.mock('../../hooks/useMatrixChat', () => ({
  useMatrixChat: () => ({
    chatState: {
      isOpen: false,
      isStarted: false,
      isLoading: false,
      error: null,
      isConnected: false,
    },
    userInfo: {
      name: '',
      phone: '',
      topicName: '',
    },
    messages: [],
    newMessage: '',
    setNewMessage: jest.fn(),
    containerRef: { current: null },
    toggleChat: jest.fn(),
    updateUserInfo: jest.fn(),
    startChat: jest.fn(),
    sendMessage: jest.fn(),
    clearSession: jest.fn(),
  }),
}));

describe('ChatWidget', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders chat toggle button', () => {
    render(<ChatWidget />);
    
    const toggleButton = screen.getByRole('button', { name: /toggle chat support/i });
    expect(toggleButton).toBeInTheDocument();
  });

  it('applies custom theme colors', () => {
    const config = {
      theme: {
        primaryColor: '#FF6B6B',
        secondaryColor: '#4ECDC4',
      },
    };

    render(<ChatWidget config={config} />);
    
    const widget = screen.getByTestId('chat-widget');
    expect(widget).toHaveStyle({
      '--chat-primary': '#FF6B6B',
      '--chat-secondary': '#4ECDC4',
    });
  });

  it('positions widget correctly', () => {
    const config = {
      theme: {
        position: 'bottom-left' as const,
      },
    };

    render(<ChatWidget config={config} />);
    
    const widget = screen.getByTestId('chat-widget');
    expect(widget).toHaveClass('bottom-4', 'left-4');
  });

  it('handles custom branding', () => {
    const config = {
      branding: {
        companyName: 'Test Company',
        welcomeMessage: 'Welcome to Test Company!',
      },
    };

    render(<ChatWidget config={config} />);
    
    // This would need to be tested when the chat is open
    // The actual implementation would pass this to SimpleChatWindow
    expect(true).toBe(true); // Placeholder assertion
  });

  it('applies custom className', () => {
    render(<ChatWidget className="custom-chat-class" />);
    
    const widget = screen.getByTestId('chat-widget');
    expect(widget).toHaveClass('custom-chat-class');
  });

  it('sets custom id', () => {
    render(<ChatWidget id="custom-chat-id" />);
    
    const widget = screen.getByTestId('chat-widget');
    expect(widget).toHaveAttribute('id', 'custom-chat-id');
  });

  it('merges config with defaults', () => {
    const partialConfig = {
      theme: {
        primaryColor: '#FF6B6B',
        // position should default to 'bottom-right'
      },
    };

    render(<ChatWidget config={partialConfig} />);
    
    const widget = screen.getByTestId('chat-widget');
    expect(widget).toHaveClass('bottom-4', 'right-4'); // default position
    expect(widget).toHaveStyle('--chat-primary: #FF6B6B');
  });

  it('handles missing config gracefully', () => {
    render(<ChatWidget />);
    
    const widget = screen.getByTestId('chat-widget');
    expect(widget).toBeInTheDocument();
    expect(widget).toHaveClass('bottom-4', 'right-4'); // default position
  });
});

describe('ChatWidget Accessibility', () => {
  it('has proper ARIA attributes', () => {
    render(<ChatWidget />);
    
    const toggleButton = screen.getByRole('button', { name: /toggle chat support/i });
    expect(toggleButton).toHaveAttribute('aria-label', 'Toggle chat support');
  });

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<ChatWidget />);
    
    const toggleButton = screen.getByRole('button', { name: /toggle chat support/i });
    
    await user.tab();
    expect(toggleButton).toHaveFocus();
    
    await user.keyboard('{Enter}');
    // Would test that chat opens, but that's handled by the mocked hook
  });

  it('respects reduced motion preferences', () => {
    // Mock reduced motion preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    render(<ChatWidget />);
    
    // Test that animations are disabled or reduced
    const widget = screen.getByTestId('chat-widget');
    expect(widget).toBeInTheDocument();
  });
});

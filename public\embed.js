/**
 * <PERSON><PERSON> Chat Widget Embed Script
 * Easy iframe embedding for websites
 *
 * Usage:
 * <script src="https://livechat.dev.longvan.vn/embed.js" data-auto-init
 *         data-primary-color="#FF6B6B"
 *         data-company-name="Your Company"
 *         data-base-url="https://livechat.dev.longvan.vn/embed">
 * </script>
 */

(function (window, document) {
  'use strict';

  // Tránh khởi tạo lại nếu đã có
  if (window.LongvanChat) return;

  const LongvanChat = {
    // Cấu hình mặc định
    config: {
      baseUrl: window.location.origin + '/embed',
      width: '400px',
      height: '600px',
      position: 'bottom-right',
      zIndex: 9999,
      primaryColor: '#3B82F6',
      secondaryColor: '#6B7280',
      companyName: 'Hỗ trợ khách hàng',
      welcomeMessage: 'Xin chào! Chúng tôi sẵn sàng hỗ trợ bạn.',
      transparent: false,
      responsive: true,
      clickThrough: false,
      // thêm các param matrix/api nếu cần
      matrixServer: null,
      apiEndpoint: null,
      roomId: null,
      threadId: null,
    },

    init(userConfig = {}) {
      // Merge cấu hình
      this.config = Object.assign({}, this.config, userConfig);
      this.createWidget();
    },

    createWidget() {
      // Tránh tạo container đôi
      if (document.getElementById('longvan-chat-container')) return;

      // Tạo container
      const c = document.createElement('div');
      c.id = 'longvan-chat-container';
      c.style.cssText = this.getContainerStyles();
      document.body.appendChild(c);

      // Tạo iframe
      const iframe = document.createElement('iframe');
      iframe.id = 'longvan-chat-iframe';
      iframe.src = this.buildIframeUrl();
      iframe.style.cssText = this.getIframeStyles();
      iframe.frameBorder = '0';
      iframe.allowTransparency = true;
      iframe.title = 'Chat Support';
      c.appendChild(iframe);

      this.addResponsiveStyles();
      this.setupEventListeners();
    },

    buildIframeUrl() {
      const params = new URLSearchParams();
      const cfg = this.config;

      // Core params (URLSearchParams tự encode)
      cfg.primaryColor   && params.set('primaryColor', cfg.primaryColor.replace('#', ''));
      cfg.secondaryColor && params.set('secondaryColor', cfg.secondaryColor.replace('#', ''));
      cfg.companyName    && params.set('companyName',    cfg.companyName);
      cfg.welcomeMessage && params.set('welcomeMessage', cfg.welcomeMessage);

      // Layout
      params.set('width',  cfg.width);
      params.set('height', cfg.height);
      params.set('transparent', String(cfg.transparent));

      // Optional matrix/api
      cfg.matrixServer && params.set('matrixServer', cfg.matrixServer);
      cfg.apiEndpoint  && params.set('apiEndpoint',  cfg.apiEndpoint);
      cfg.roomId       && params.set('roomId',       cfg.roomId);
      cfg.threadId     && params.set('threadId',     cfg.threadId);

      const url = new URL(cfg.baseUrl);
      url.search = params.toString();
      return url.toString();
    },

    getContainerStyles() {
      const pos = this.config.position;
      let posCss;
      switch (pos) {
        case 'bottom-left':  posCss = 'bottom:20px; left:20px;'; break;
        case 'top-right':    posCss = 'top:20px; right:20px;';  break;
        case 'top-left':     posCss = 'top:20px; left:20px;';   break;
        default:             posCss = 'bottom:20px; right:20px;'; break;
      }
      return `
        position: fixed;
        ${posCss}
        width: ${this.config.width};
        height: ${this.config.height};
        max-width: 400px;
        max-height: 600px;
        z-index: ${this.config.zIndex};
        overflow: visible;
        background: transparent;
        transition: all 0.3s ease;
        pointer-events: none;
      `;
    },

    getIframeStyles() {
      return `
        width: 100%;
        height: 100%;
        border: none;
        background: transparent;
        display: block;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        pointer-events: auto;
      `;
    },

    addResponsiveStyles() {
      if (document.getElementById('longvan-chat-responsive-styles')) return;
      const style = document.createElement('style');
      style.id = 'longvan-chat-responsive-styles';
      style.textContent = `
        @media (max-width: 768px) {
          #longvan-chat-container {
            top: 0; bottom: 0; left: 0; right: 0 !important;
            width: 100% !important; height: 100% !important;
            max-width: none; max-height: none;
            z-index: ${this.config.zIndex + 1} !important;
          }
        }
        @media (max-width: 480px) {
          #longvan-chat-container { box-shadow: none !important; }
        }
      `;
      document.head.appendChild(style);
    },

    setupEventListeners() {
      this._resizeHandler = this.handleResize.bind(this);
      this._messageHandler = this.handleMessage.bind(this);
      window.addEventListener('resize', this._resizeHandler);
      window.addEventListener('message', this._messageHandler);
    },

    handleResize() { /* tuỳ biến nếu cần */ },

    handleMessage(event) {
      const origin = new URL(this.config.baseUrl).origin;
      if (event.origin !== origin) return;
      if (event.data?.source !== 'longvan-chat') return;
      const { type, data } = event.data;

      switch (type) {
        case 'iframe-ready':   this.onIframeReady(data); break;
        case 'chat-started':   this.onChatStarted(data); break;
        case 'chat-opened':    this.onChatOpened(data); break;
        case 'chat-closed':    this.onChatClosed(data); break;
        case 'message-sent':   this.onMessageSent(data); break;
        case 'message-received': this.onMessageReceived(data); break;
        case 'resize-request': this.handleResizeRequest(data); break;
      }
    },

    // Hook methods to override
    onIframeReady()   { console.log('Iframe ready'); },
    onChatStarted(d)  { console.log('Chat started', d); },
    onChatOpened()    {},
    onChatClosed()    {},
    onMessageSent()   {},
    onMessageReceived() {},

    handleResizeRequest(data) {
      const c = document.getElementById('longvan-chat-container');
      if (!c || !data) return;
      data.width  && (c.style.width  = data.width  + 'px');
      data.height && (c.style.height = data.height + 'px');
    },

    // Public API
    show()    { document.getElementById('longvan-chat-container')?.style.display = 'block'; },
    hide()    { document.getElementById('longvan-chat-container')?.style.display = 'none'; },
    destroy() {
      document.getElementById('longvan-chat-container')?.remove();
      document.getElementById('longvan-chat-responsive-styles')?.remove();
      window.removeEventListener('resize', this._resizeHandler);
      window.removeEventListener('message', this._messageHandler);
      delete window.LongvanChat;
    },
  };

  // Đưa ra global
  window.LongvanChat = LongvanChat;

  // Tự init nếu có data-auto-init
  document.addEventListener('DOMContentLoaded', () => {
    const script = document.querySelector('script[src*="embed.js"][data-auto-init]');
    if (script) {
      const attrs = script.dataset;
      LongvanChat.init({
        primaryColor:   attrs.primaryColor,
        secondaryColor: attrs.secondaryColor,
        companyName:    attrs.companyName,
        welcomeMessage: attrs.welcomeMessage,
        position:       attrs.position,
        baseUrl:        attrs.baseUrl,
      });
    }
  });
})(window, document);

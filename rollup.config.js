// rollup.config.js
import resolve from "@rollup/plugin-node-resolve";
import commonjs from "@rollup/plugin-commonjs";
import babel from "@rollup/plugin-babel";

export default {
  input: "src/register-widget.js",
  output: {
    file: "dist/my-widget.js",
    format: "umd",
    name: "MyWidget",
    globals: { react: "React", "react-dom": "ReactDOM" },
  },
  external: ["react", "react-dom"],
  plugins: [
    resolve(),
    commonjs(),
    babel({ babelHelpers: "bundled", presets: ["@babel/preset-react"] }),
  ],
};

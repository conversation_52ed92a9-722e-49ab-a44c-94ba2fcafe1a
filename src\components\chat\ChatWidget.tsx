import React from 'react';
import { ChatThemeProvider } from './ChatThemeProvider';
import SimpleChatWindow from './SimpleChatWindow';

export interface ChatWidgetConfig {
  matrixServer?: string;
  apiEndpoint?: string;
  roomId?: string;
  threadId?: string;
  theme?: {
    primaryColor?: string;
    secondaryColor?: string;
    position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  };
  branding?: {
    logo?: string;
    companyName?: string;
    welcomeMessage?: string;
  };
  callbacks?: {
    onChatStart?: (userInfo: any) => void;
    onMessageSent?: (message: any) => void;
    onChatEnd?: () => void;
  };
}

interface ChatWidgetProps {
  config?: ChatWidgetConfig;
  className?: string;
  id?: string;
}

const defaultConfig: ChatWidgetConfig = {
  matrixServer: process.env.REACT_APP_MATRIX_SERVER || 'https://matrix-synapse.longvan.vn',
  apiEndpoint: process.env.REACT_APP_CHAT_API || 'https://com-hub.dev.longvan.vn/com-hub/v1/web-hook/send-message-to-matrix/674fcd11b1538b122be026d0',
  theme: {
    primaryColor: '#3B82F6',
    secondaryColor: '#6B7280',
    position: 'bottom-right',
  },
  branding: {
    companyName: 'Hỗ trợ khách hàng',
    welcomeMessage: 'Xin chào! 👋 Chúng tôi sẵn sàng hỗ trợ bạn.',
  }
};

const ChatWidget: React.FC<ChatWidgetProps> = ({ 
  config = {}, 
  className = '',
  id = 'chat-widget'
}) => {
  const mergedConfig = { ...defaultConfig, ...config };
  
  // Position styles
  const positionStyles = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
  };

  const positionClass = positionStyles[mergedConfig.theme?.position || 'bottom-right'];

  return (
    <ChatThemeProvider theme={mergedConfig.theme!}>
      <div 
        id={id}
        className={`chat-widget fixed ${positionClass} z-[9999] ${className}`}
        style={{
          '--chat-primary': mergedConfig.theme?.primaryColor,
          '--chat-secondary': mergedConfig.theme?.secondaryColor,
        } as React.CSSProperties}
      >
        <SimpleChatWindow config={mergedConfig} />
      </div>
    </ChatThemeProvider>
  );
};

export default ChatWidget;

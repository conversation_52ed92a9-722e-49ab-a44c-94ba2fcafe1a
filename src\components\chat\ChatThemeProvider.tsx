import React, { createContext, useContext } from 'react';

export interface ChatTheme {
  primaryColor?: string;
  secondaryColor?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

const ChatThemeContext = createContext<ChatTheme | null>(null);

export const useChatTheme = () => {
  const context = useContext(ChatThemeContext);
  if (!context) {
    return {
      primaryColor: '#3B82F6',
      secondaryColor: '#6B7280',
      position: 'bottom-right' as const,
    };
  }
  return context;
};

interface ChatThemeProviderProps {
  theme: ChatTheme;
  children: React.ReactNode;
}

export const ChatThemeProvider: React.FC<ChatThemeProviderProps> = ({ 
  theme, 
  children 
}) => {
  return (
    <ChatThemeContext.Provider value={theme}>
      {children}
    </ChatThemeContext.Provider>
  );
};
